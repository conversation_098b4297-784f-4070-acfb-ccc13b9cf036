import click
from typing import Dict, Any
from src.data.yahoo_client import Yahoo<PERSON><PERSON>
from src.engine.decision_engine import DecisionEngine
from src.engine.enhanced_recommendation_engine import EnhancedRecommendationEngine
from src.cli.formatters import get_formatter
import click
from typing import Dict, Any

def fetch_and_analyze(symbol: str, period: str = '1y', risk: str = 'moderate', enhanced: bool = False) -> Dict[str, Any]:
    """Fetch stock data and perform analysis"""
    # Initialize clients
    client = YahooClient()
    
    if enhanced:
        # Use enhanced recommendation engine for comprehensive analysis
        engine = EnhancedRecommendationEngine(risk_tolerance=risk)
    else:
        # Use basic decision engine
        engine = DecisionEngine()
    
    # Validate and normalize symbol
    symbol = symbol.upper()
    if not client.is_valid_bse_symbol(symbol):
        raise ValueError(f"Invalid BSE symbol format: {symbol.replace('.BO', '')}")
    
    # Fetch stock data
    try:
        stock_data = client.fetch_stock_data(symbol, period)
        if not stock_data or stock_data.data.empty:
            raise ConnectionError(f"Failed to fetch data for {symbol}: No data available")
    except Exception as e:
        if isinstance(e, ConnectionError):
            raise e
        raise ConnectionError(f"Failed to fetch data for {symbol}: {str(e)}") from e
    
    # Perform analysis based on engine type
    if enhanced:
        # Use enhanced analysis
        result = engine.analyze_stock_enhanced(stock_data, risk_tolerance=risk)
        # Flatten enhanced result structure for formatter compatibility
        enhanced_rec = result.get('enhanced_recommendation', {})
        flattened_result = {
            'symbol': result.get('symbol', symbol),
            'recommendation': enhanced_rec.get('action', 'HOLD'),
            'confidence': enhanced_rec.get('confidence', 0.0),
            'target_price': enhanced_rec.get('target_price'),
            'stop_loss': enhanced_rec.get('stop_loss'),
            'risk_level': enhanced_rec.get('risk_level', 'MEDIUM'),
            'current_price': stock_data.current_price,
            # Include technical analysis
            **result.get('technical_analysis', {}),
            # Include risk assessment
            'risk_assessment': result.get('risk_analysis', {}),
            # Include enhanced factor scores for detailed display
            'factor_scores': enhanced_rec.get('factor_scores', {}),
            'enhanced_recommendation': enhanced_rec  # Keep full enhanced data
        }
        return flattened_result
    else:
        # Use basic analysis
        result = engine.analyze_stock(stock_data, risk_tolerance=risk)
        return result

@click.command()
@click.argument('symbol')
@click.option('--period', type=click.Choice(['1m', '3m', '6m', '1y', '2y']), default='1y', help='Analysis period')
@click.option('--risk', type=click.Choice(['conservative', 'moderate', 'aggressive']), default='moderate', help='Risk tolerance')
@click.option('--format', 'output_format', type=click.Choice(['table', 'json', 'csv']), default='table', help='Output format')
@click.option('--output', help='Output file path')
@click.option('--enhanced/--no-enhanced', default=True, help='Use enhanced multi-factor analysis (default: enabled)')
def analyze_stock(symbol, period, risk, output_format, output, enhanced):
    """Analyze a BSE stock with comprehensive metrics display"""
    try:
        # Normalize symbol to uppercase
        symbol = symbol.upper()
        
        # Fetch and analyze stock
        result = fetch_and_analyze(symbol, period=period, risk=risk, enhanced=enhanced)
        
        # Format output
        formatter = get_formatter(output_format)
        formatted_output = formatter.format_analysis(result)
        
        # Output to file or console
        if output:
            # Create directory if it doesn't exist
            import os
            os.makedirs(os.path.dirname(output), exist_ok=True)
            
            with open(output, 'w') as f:
                f.write(formatted_output)
            click.echo(f"Analysis saved to {output}")
        else:
            click.echo(formatted_output)
            
    except (ValueError, ConnectionError) as e:
        formatter = get_formatter(output_format)
        error_output = formatter.format_error(str(e))
        click.echo(error_output, err=True)
        raise click.ClickException(str(e))
    except Exception as e:
        formatter = get_formatter(output_format)
        error_output = formatter.format_error(f"Unexpected error occurred: {str(e)}")
        click.echo(error_output, err=True)
        raise click.ClickException(f"Unexpected error occurred: {str(e)}")

@click.group()
@click.version_option(version='0.1.0')
def cli():
    """BSE Stock Analysis Tool"""
    pass

# Add commands to CLI group
cli.add_command(analyze_stock, name='analyze')