import yfinance as yf
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List, Tuple
from src.data.yahoo_client import StockData

class FundamentalAnalysis:
    """Calculate fundamental analysis metrics for stock analysis"""
    
    def __init__(self):
        """Initialize fundamental analysis calculator"""
        pass
    
    # Valuation Metrics
    
    def calculate_pe_ratio(self, ticker_info: Dict[str, Any], 
                          current_price: Optional[float] = None, 
                          earnings_per_share: Optional[float] = None) -> Optional[float]:
        """
        Calculate P/E (Price-to-Earnings) ratio
        
        Args:
            ticker_info: Dictionary from yfinance ticker.info
            current_price: Current stock price (for manual calculation)
            earnings_per_share: EPS (for manual calculation)
            
        Returns:
            P/E ratio or None if cannot be calculated
        """
        if not self._validate_ticker_info(ticker_info):
            return None
        
        # Try to get from ticker info first (prefer trailing over forward)
        if 'trailingPE' in ticker_info and ticker_info['trailingPE'] is not None:
            pe_ratio = ticker_info['trailingPE']
            if pe_ratio > 0:  # Validate positive P/E
                return float(pe_ratio)
        
        if 'forwardPE' in ticker_info and ticker_info['forwardPE'] is not None:
            pe_ratio = ticker_info['forwardPE']
            if pe_ratio > 0:  # Validate positive P/E
                return float(pe_ratio)
        
        # Manual calculation if ticker data not available
        if current_price is not None and earnings_per_share is not None:
            if earnings_per_share > 0:  # Avoid division by zero and negative EPS
                return float(current_price / earnings_per_share)
        
        return None
    
    def calculate_pb_ratio(self, ticker_info: Dict[str, Any],
                          current_price: Optional[float] = None,
                          book_value_per_share: Optional[float] = None) -> Optional[float]:
        """
        Calculate P/B (Price-to-Book) ratio
        
        Args:
            ticker_info: Dictionary from yfinance ticker.info
            current_price: Current stock price (for manual calculation)  
            book_value_per_share: Book value per share (for manual calculation)
            
        Returns:
            P/B ratio or None if cannot be calculated
        """
        if not self._validate_ticker_info(ticker_info):
            return None
        
        # Try to get from ticker info first
        if 'priceToBook' in ticker_info and ticker_info['priceToBook'] is not None:
            pb_ratio = ticker_info['priceToBook']
            if pb_ratio > 0:  # Validate positive P/B
                return float(pb_ratio)
        
        # Manual calculation if ticker data not available
        if current_price is not None and book_value_per_share is not None:
            if book_value_per_share > 0:  # Avoid division by zero and negative book value
                return float(current_price / book_value_per_share)
        
        return None
    
    # Profitability Metrics
    
    def calculate_roe(self, ticker_info: Dict[str, Any],
                     net_income: Optional[float] = None,
                     shareholders_equity: Optional[float] = None) -> Optional[float]:
        """
        Calculate ROE (Return on Equity) as percentage
        
        Args:
            ticker_info: Dictionary from yfinance ticker.info
            net_income: Net income (for manual calculation)
            shareholders_equity: Shareholders equity (for manual calculation)
            
        Returns:
            ROE as percentage or None if cannot be calculated
        """
        if not self._validate_ticker_info(ticker_info):
            return None
        
        # Try to get from ticker info first
        if 'returnOnEquity' in ticker_info and ticker_info['returnOnEquity'] is not None:
            roe = ticker_info['returnOnEquity']
            if isinstance(roe, (int, float)):
                return float(roe * 100)  # Convert to percentage
        
        # Manual calculation if ticker data not available
        if net_income is not None and shareholders_equity is not None:
            if shareholders_equity > 0:  # Avoid division by zero
                return float((net_income / shareholders_equity) * 100)
        
        return None
    
    def calculate_roa(self, ticker_info: Dict[str, Any],
                     net_income: Optional[float] = None,
                     total_assets: Optional[float] = None) -> Optional[float]:
        """
        Calculate ROA (Return on Assets) as percentage
        
        Args:
            ticker_info: Dictionary from yfinance ticker.info
            net_income: Net income (for manual calculation)
            total_assets: Total assets (for manual calculation)
            
        Returns:
            ROA as percentage or None if cannot be calculated
        """
        if not self._validate_ticker_info(ticker_info):
            return None
        
        # Try to get from ticker info first
        if 'returnOnAssets' in ticker_info and ticker_info['returnOnAssets'] is not None:
            roa = ticker_info['returnOnAssets']
            if isinstance(roa, (int, float)):
                return float(roa * 100)  # Convert to percentage
        
        # Manual calculation if ticker data not available
        if net_income is not None and total_assets is not None:
            if total_assets > 0:  # Avoid division by zero
                return float((net_income / total_assets) * 100)
        
        return None
    
    def calculate_debt_to_equity(self, ticker_info: Dict[str, Any],
                                total_debt: Optional[float] = None,
                                shareholders_equity: Optional[float] = None) -> Optional[float]:
        """
        Calculate Debt-to-Equity ratio
        
        Args:
            ticker_info: Dictionary from yfinance ticker.info
            total_debt: Total debt (for manual calculation)
            shareholders_equity: Shareholders equity (for manual calculation)
            
        Returns:
            D/E ratio or None if cannot be calculated
        """
        if not self._validate_ticker_info(ticker_info):
            return None
        
        # Try to get from ticker info first
        if 'debtToEquity' in ticker_info and ticker_info['debtToEquity'] is not None:
            de_ratio = ticker_info['debtToEquity']
            if isinstance(de_ratio, (int, float)) and de_ratio >= 0:
                return float(de_ratio)
        
        # Manual calculation if ticker data not available
        if total_debt is not None and shareholders_equity is not None:
            if shareholders_equity > 0:  # Avoid division by zero
                return float(total_debt / shareholders_equity)
        
        return None
    
    # Growth Metrics
    
    def calculate_revenue_growth(self, ticker_info: Dict[str, Any]) -> Tuple[Optional[float], Optional[float]]:
        """
        Calculate revenue growth YoY and QoQ as percentages
        
        Args:
            ticker_info: Dictionary from yfinance ticker.info
            
        Returns:
            Tuple of (YoY growth %, QoQ growth %) or (None, None)
        """
        if not self._validate_ticker_info(ticker_info):
            return None, None
        
        yoy_growth = None
        qoq_growth = None
        
        # YoY revenue growth
        if 'revenueGrowth' in ticker_info and ticker_info['revenueGrowth'] is not None:
            yoy = ticker_info['revenueGrowth']
            if isinstance(yoy, (int, float)):
                yoy_growth = float(yoy * 100)  # Convert to percentage
        
        # QoQ revenue growth
        if 'quarterlyRevenueGrowth' in ticker_info and ticker_info['quarterlyRevenueGrowth'] is not None:
            qoq = ticker_info['quarterlyRevenueGrowth']
            if isinstance(qoq, (int, float)):
                qoq_growth = float(qoq * 100)  # Convert to percentage
        
        return yoy_growth, qoq_growth
    
    def calculate_earnings_growth(self, ticker_info: Dict[str, Any]) -> Tuple[Optional[float], Optional[float]]:
        """
        Calculate earnings growth YoY and QoQ as percentages
        
        Args:
            ticker_info: Dictionary from yfinance ticker.info
            
        Returns:
            Tuple of (YoY growth %, QoQ growth %) or (None, None)
        """
        if not self._validate_ticker_info(ticker_info):
            return None, None
        
        yoy_growth = None
        qoq_growth = None
        
        # YoY earnings growth
        if 'earningsGrowth' in ticker_info and ticker_info['earningsGrowth'] is not None:
            yoy = ticker_info['earningsGrowth']
            if isinstance(yoy, (int, float)):
                yoy_growth = float(yoy * 100)  # Convert to percentage
        
        # QoQ earnings growth
        if 'quarterlyEarningsGrowth' in ticker_info and ticker_info['quarterlyEarningsGrowth'] is not None:
            qoq = ticker_info['quarterlyEarningsGrowth']
            if isinstance(qoq, (int, float)):
                qoq_growth = float(qoq * 100)  # Convert to percentage
        
        return yoy_growth, qoq_growth
    
    def calculate_qoq_growth(self, quarterly_data: List[float]) -> Optional[float]:
        """
        Calculate quarter-over-quarter growth from historical data
        
        Args:
            quarterly_data: List of quarterly values (most recent first)
            
        Returns:
            QoQ growth percentage or None
        """
        if len(quarterly_data) < 2:
            return None
        
        current_quarter = quarterly_data[0]
        previous_quarter = quarterly_data[1]
        
        if previous_quarter > 0:
            return float(((current_quarter - previous_quarter) / previous_quarter) * 100)
        
        return None
    
    def calculate_yoy_growth(self, quarterly_data: List[float], quarters_back: int = 4) -> Optional[float]:
        """
        Calculate year-over-year growth from historical data
        
        Args:
            quarterly_data: List of quarterly values (most recent first)
            quarters_back: Number of quarters to look back (default: 4 for YoY)
            
        Returns:
            YoY growth percentage or None
        """
        if len(quarterly_data) <= quarters_back:  # Need more than quarters_back data points
            return None
        
        current_value = quarterly_data[0]
        previous_year_value = quarterly_data[quarters_back]
        
        if previous_year_value > 0:
            return float(((current_value - previous_year_value) / previous_year_value) * 100)
        
        return None
    
    # Dividend Metrics
    
    def calculate_dividend_yield(self, ticker_info: Dict[str, Any],
                               annual_dividend: Optional[float] = None,
                               current_price: Optional[float] = None) -> Optional[float]:
        """
        Calculate dividend yield as percentage
        
        Args:
            ticker_info: Dictionary from yfinance ticker.info
            annual_dividend: Annual dividend per share (for manual calculation)
            current_price: Current stock price (for manual calculation)
            
        Returns:
            Dividend yield as percentage or None
        """
        if not self._validate_ticker_info(ticker_info):
            return None
        
        # Try to get from ticker info first
        if 'dividendYield' in ticker_info and ticker_info['dividendYield'] is not None:
            div_yield = ticker_info['dividendYield']
            if isinstance(div_yield, (int, float)) and div_yield >= 0:
                return float(div_yield * 100)  # Convert to percentage
        
        # Manual calculation if ticker data not available
        if annual_dividend is not None and current_price is not None:
            if current_price > 0:  # Avoid division by zero
                return float((annual_dividend / current_price) * 100)
        
        return None
    
    # Comprehensive Analysis
    
    def get_fundamental_summary(self, stock_data: StockData) -> Dict[str, Any]:
        """
        Get comprehensive fundamental analysis summary
        
        Args:
            stock_data: StockData object containing price data
            
        Returns:
            Dictionary with all fundamental metrics
        """
        # Fetch fundamental data using yfinance
        ticker = yf.Ticker(stock_data.symbol)
        ticker_info = ticker.info
        
        # Calculate all fundamental metrics
        summary = {
            # Valuation metrics
            'pe_ratio': self.calculate_pe_ratio(ticker_info),
            'pb_ratio': self.calculate_pb_ratio(ticker_info),
            'enterprise_to_revenue': ticker_info.get('enterpriseToRevenue'),
            'enterprise_to_ebitda': ticker_info.get('enterpriseToEbitda'),
            
            # Profitability metrics
            'roe': self.calculate_roe(ticker_info),
            'roa': self.calculate_roa(ticker_info),
            'debt_to_equity': self.calculate_debt_to_equity(ticker_info),
            'current_ratio': ticker_info.get('currentRatio'),
            'quick_ratio': ticker_info.get('quickRatio'),
            
            # Growth metrics
            'revenue_growth_yoy': None,
            'revenue_growth_qoq': None,
            'earnings_growth_yoy': None,
            'earnings_growth_qoq': None,
            
            # Dividend metrics
            'dividend_yield': self.calculate_dividend_yield(ticker_info),
            'dividend_rate': ticker_info.get('dividendRate'),
            'payout_ratio': ticker_info.get('payoutRatio'),
            
            # Company info
            'company_name': ticker_info.get('longName'),
            'sector': ticker_info.get('sector'),
            'industry': ticker_info.get('industry'),
            'country': ticker_info.get('country'),
            'currency': ticker_info.get('currency'),
            'market_cap': ticker_info.get('marketCap'),
        }
        
        # Calculate growth metrics
        revenue_yoy, revenue_qoq = self.calculate_revenue_growth(ticker_info)
        earnings_yoy, earnings_qoq = self.calculate_earnings_growth(ticker_info)
        
        summary['revenue_growth_yoy'] = revenue_yoy
        summary['revenue_growth_qoq'] = revenue_qoq
        summary['earnings_growth_yoy'] = earnings_yoy
        summary['earnings_growth_qoq'] = earnings_qoq
        
        return summary
    
    # Sector Comparison
    
    def compare_with_sector(self, company_metrics: Dict[str, Any], 
                           sector_averages: Dict[str, Any]) -> Dict[str, Any]:
        """
        Compare company metrics with sector averages
        
        Args:
            company_metrics: Dictionary of company financial metrics
            sector_averages: Dictionary of sector average metrics
            
        Returns:
            Dictionary with comparison results (positive = above sector average)
        """
        comparison = {}
        
        metric_mapping = {
            'pe_ratio': 'pe_vs_sector',
            'pb_ratio': 'pb_vs_sector', 
            'roe': 'roe_vs_sector',
            'debt_to_equity': 'debt_to_equity_vs_sector'
        }
        
        for metric, result_key in metric_mapping.items():
            company_value = company_metrics.get(metric)
            sector_value = sector_averages.get(metric)
            
            if company_value is not None and sector_value is not None:
                if metric in ['roe']:  # Higher is better
                    comparison[result_key] = company_value - sector_value
                elif metric in ['debt_to_equity']:  # Lower is better
                    comparison[result_key] = sector_value - company_value  
                else:  # P/E, P/B - context dependent, just show difference
                    comparison[result_key] = company_value - sector_value
            else:
                comparison[result_key] = None
        
        return comparison
    
    # Validation and Utility Methods
    
    def _validate_ticker_info(self, ticker_info: Any) -> bool:
        """Validate ticker info data structure"""
        return ticker_info is not None and isinstance(ticker_info, dict)
    
    def check_ratio_reasonableness(self, ratios: Dict[str, Any]) -> Dict[str, Any]:
        """
        Check if calculated ratios are within reasonable bounds
        
        Args:
            ratios: Dictionary of calculated financial ratios
            
        Returns:
            Dictionary with reasonableness flags and warnings
        """
        flags = []
        warnings = []
        
        # Define reasonable bounds for common ratios
        bounds = {
            'pe_ratio': (0, 100),      # P/E above 100 is unusual
            'pb_ratio': (0, 20),       # P/B above 20 is unusual
            'roe': (-50, 100),         # ROE above 100% is unusual
            'roa': (-20, 50),          # ROA above 50% is unusual
            'debt_to_equity': (0, 5),  # D/E above 5 is high leverage
            'dividend_yield': (0, 20)  # Div yield above 20% is unusual
        }
        
        for ratio_name, (min_val, max_val) in bounds.items():
            ratio_value = ratios.get(ratio_name)
            
            if ratio_value is not None:
                if ratio_value < min_val or ratio_value > max_val:
                    flags.append(f'{ratio_name}: {ratio_value} outside normal range ({min_val}-{max_val})')
                
                # Specific warnings
                if ratio_name == 'pe_ratio' and ratio_value > 50:
                    warnings.append('Very high P/E ratio - may indicate overvaluation')
                elif ratio_name == 'debt_to_equity' and ratio_value > 2:
                    warnings.append('High debt-to-equity ratio - leverage risk')
        
        return {
            'flags': flags,
            'warnings': warnings
        }