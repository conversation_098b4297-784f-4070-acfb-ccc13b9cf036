from src.utilities.helpers import floats_close
import yfinance as yf
import pandas as pd
from typing import Optional
from dataclasses import dataclass

@dataclass
class StockData:
    """Data class for stock information"""
    symbol: str
    data: pd.DataFrame
    period: str
    
    def __post_init__(self):
        """Validate data after initialization"""
        if self.data.empty:
            raise ValueError(f"No data found for symbol {self.symbol}")
        
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        missing_columns = [col for col in required_columns if col not in self.data.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
    
    @property
    def current_price(self) -> float:
        """Get the most recent closing price"""
        return float(self.data['Close'].iloc[-1])
    
    @property
    def price_change(self) -> float:
        """Get absolute price change from previous day"""
        if len(self.data) < 2:
            return 0.0
        return float(self.data['Close'].iloc[-1] - self.data['Close'].iloc[-2])
    
    @property
    def price_change_percent(self) -> float:
        """Get percentage price change from previous day"""
        if len(self.data) < 2:
            return 0.0
        prev_price = self.data['Close'].iloc[-2]
        return float((self.price_change / prev_price) * 100)

class YahooClient:
    """Client for fetching stock data from Yahoo Finance"""
    
    def __init__(self):
        self.valid_periods = ["1m", "3m", "6m", "1y", "2y"]
    
    def is_valid_bse_symbol(self, symbol: str) -> bool:
        """Validate BSE stock symbol format"""
        if not symbol or not isinstance(symbol, str):
            return False
            
        # BSE symbols should end with .BO
        if not symbol.endswith('.BO'):
            return False
            
        # Extract the symbol part (everything before .BO)
        symbol_part = symbol[:-3]  # Remove '.BO'
        
        # Symbol part should not be empty
        if not symbol_part:
            return False
            
        # Symbol part should contain only uppercase letters and numbers
        # Common BSE symbols are like RELIANCE, TCS, INFY, HDFC, etc.
        if not symbol_part.replace('&', '').replace('-', '').isalnum():
            return False
            
        # Symbol part should start with a letter
        if not symbol_part[0].isalpha():
            return False
            
        return True
    
    def is_valid_period(self, period: str) -> bool:
        """Validate if period is supported"""
        if not period or not isinstance(period, str):
            return False
            
        return period in self.valid_periods
    
    def fetch_stock_data(self, symbol: str, period: str = "1y") -> StockData:
        """Fetch stock data from Yahoo Finance"""
        # Validate inputs
        if not isinstance(symbol, str):
            raise ValueError("Symbol must be a string")
            
        if not self.is_valid_bse_symbol(symbol):
            raise ValueError(f"Invalid BSE symbol format: {symbol}")
            
        if not self.is_valid_period(period):
            raise ValueError(f"Invalid period: {period}. Valid periods are: {self.valid_periods}")
        
        try:
            # Create yfinance Ticker object
            ticker = yf.Ticker(symbol)
            
            # Fetch historical data
            data = ticker.history(period=period)
            
            # Check if data is empty
            if data.empty:
                raise ValueError(f"No data found for symbol {symbol}. Symbol may not exist or have no trading history.")
            
            # Create and return StockData object
            return StockData(
                symbol=symbol,
                data=data,
                period=period
            )
            
        except Exception as e:
            # Re-raise ValueError with more context, but preserve other exceptions
            if isinstance(e, ValueError):
                raise e
            else:
                # This catches network errors, API errors, etc.
                raise ConnectionError(f"Failed to fetch data for {symbol}: {str(e)}") from e
    
    def fetch_fundamental_data(self, symbol: str) -> dict:
        """Fetch fundamental data from Yahoo Finance"""
        # Validate symbol
        if not self.is_valid_bse_symbol(symbol):
            raise ValueError(f"Invalid BSE symbol format: {symbol}")
        
        try:
            # Create yfinance Ticker object
            ticker = yf.Ticker(symbol)
            ticker_info = ticker.info
            
            # Structure the fundamental data according to test expectations
            fundamental_data = {
                'valuation': {
                    'pe_ratio': self._get_valid_metric(ticker_info.get('trailingPE')) or self._get_valid_metric(ticker_info.get('forwardPE')),
                    'pb_ratio': self._get_valid_metric(ticker_info.get('priceToBook')),
                    'enterprise_to_revenue': self._get_valid_metric(ticker_info.get('enterpriseToRevenue')),
                    'enterprise_to_ebitda': self._get_valid_metric(ticker_info.get('enterpriseToEbitda')),
                },
                'profitability': {
                    'roe': self._convert_to_percentage(ticker_info.get('returnOnEquity')),
                    'roa': self._convert_to_percentage(ticker_info.get('returnOnAssets')),
                    'debt_to_equity': self._get_valid_metric(ticker_info.get('debtToEquity')),
                    'current_ratio': self._get_valid_metric(ticker_info.get('currentRatio')),
                    'quick_ratio': self._get_valid_metric(ticker_info.get('quickRatio')),
                },
                'growth': {
                    'revenue_growth_yoy': self._convert_to_percentage(ticker_info.get('revenueGrowth')),
                    'revenue_growth_qoq': self._convert_to_percentage(ticker_info.get('quarterlyRevenueGrowth')),
                    'earnings_growth_yoy': self._convert_to_percentage(ticker_info.get('earningsGrowth')),
                    'earnings_growth_qoq': self._convert_to_percentage(ticker_info.get('quarterlyEarningsGrowth')),
                },
                'dividend': {
                    'dividend_yield': self._convert_to_percentage(ticker_info.get('dividendYield')),
                    'dividend_rate': self._get_valid_metric(ticker_info.get('dividendRate')),
                    'payout_ratio': self._convert_to_percentage(ticker_info.get('payoutRatio')),
                },
                'company_info': {
                    'company_name': ticker_info.get('longName'),
                    'sector': ticker_info.get('sector'),
                    'industry': ticker_info.get('industry'),
                    'country': ticker_info.get('country'),
                    'currency': ticker_info.get('currency'),
                    'market_cap': ticker_info.get('marketCap'),
                }
            }
            
            return fundamental_data
            
        except Exception as e:
            raise ConnectionError(f"Failed to fetch fundamental data for {symbol}: {str(e)}") from e
    
    def fetch_complete_stock_info(self, symbol: str, period: str = "1y") -> dict:
        """Fetch complete stock information (price data + fundamental data)"""
        try:
            # Fetch price data
            stock_data = self.fetch_stock_data(symbol, period)
            
            # Fetch fundamental data
            try:
                fundamental_data = self.fetch_fundamental_data(symbol)
            except Exception as e:
                # If fundamental data fails, still return price data with empty fundamentals
                fundamental_data = self._get_empty_fundamental_structure()
            
            return {
                'stock_data': stock_data,
                'fundamental_data': fundamental_data
            }
            
        except Exception as e:
            raise ConnectionError(f"Failed to fetch complete stock info for {symbol}: {str(e)}") from e
    
    def _get_valid_metric(self, value) -> Optional[float]:
        """Validate and return metric value, filtering out invalid values"""
        if value is None:
            return None
        
        try:
            float_value = float(value)
            # Filter out invalid values
            if float_value < 0:  # Negative values are invalid for most ratios
                return None
            if floats_close(float_value, 0.0):  # Zero values are often invalid for ratios like P/B
                return None
            return float_value
        except (ValueError, TypeError):
            return None
    
    def _convert_to_percentage(self, value) -> Optional[float]:
        """Convert decimal to percentage, with validation"""
        if value is None:
            return None
        
        try:
            float_value = float(value)
            # ROE > 1 indicates it's already in percentage form (invalid)
            if abs(float_value) > 5:  # Assume values > 5 are already percentages or invalid
                return None
            return float_value * 100
        except (ValueError, TypeError):
            return None
    
    def _get_empty_fundamental_structure(self) -> dict:
        """Return empty fundamental data structure with None values"""
        return {
            'valuation': {
                'pe_ratio': None, 'pb_ratio': None, 'enterprise_to_revenue': None, 'enterprise_to_ebitda': None
            },
            'profitability': {
                'roe': None, 'roa': None, 'debt_to_equity': None, 'current_ratio': None, 'quick_ratio': None
            },
            'growth': {
                'revenue_growth_yoy': None, 'revenue_growth_qoq': None, 'earnings_growth_yoy': None, 'earnings_growth_qoq': None
            },
            'dividend': {
                'dividend_yield': None, 'dividend_rate': None, 'payout_ratio': None
            },
            'company_info': {
                'company_name': None, 'sector': None, 'industry': None, 'country': None, 'currency': None, 'market_cap': None
            }
        }