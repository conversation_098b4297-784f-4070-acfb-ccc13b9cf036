from typing import Dict, Any, Optional, List

from src.data.yahoo_client import StockData
from src.engine.technical_scorer import TechnicalScorer
from src.engine.fundamental_scorer import FundamentalScorer
from src.engine.risk_scorer import RiskScorer
from src.engine.market_context_analyzer import MarketContextAnalyzer
from src.analysis.indicators import TechnicalIndicators
from src.analysis.fundamental import FundamentalAnalysis

class EnhancedRecommendationEngine:
    """Enhanced recommendation engine with multi-factor analysis"""
    
    def __init__(self, factor_weights: Optional[Dict[str, float]] = None, risk_tolerance: str = 'moderate'):
        """
        Initialize enhanced recommendation engine
        
        Args:
            factor_weights: Custom weights for different factors
                          Must sum to 1.0 and include all required factors
            risk_tolerance: Risk tolerance level ('conservative', 'moderate', 'aggressive')
        """
        self.risk_tolerance = risk_tolerance.lower()
        # Set default factor weights
        if factor_weights is None:
            factor_weights = {
                'technical': 0.35,
                'fundamental': 0.30,
                'risk': 0.25,
                'market_context': 0.10
            }
        
        # Validate factor weights
        self._validate_factor_weights(factor_weights)
        self.factor_weights = factor_weights
        
        # Initialize component scorers with risk tolerance
        self.technical_scorer = TechnicalScorer()
        self.fundamental_scorer = FundamentalScorer(risk_tolerance=self.risk_tolerance)
        self.risk_scorer = RiskScorer()
        self.market_context_analyzer = MarketContextAnalyzer()
        
        # Initialize analysis components
        self.technical_indicators = TechnicalIndicators()
        self.fundamental_analysis = FundamentalAnalysis()
    
    def _validate_factor_weights(self, weights: Dict[str, float]) -> None:
        """Validate factor weights"""
        required_factors = {'technical', 'fundamental', 'risk', 'market_context'}
        
        # Check all required factors are present
        missing_factors = required_factors - set(weights.keys())
        if missing_factors:
            raise ValueError(f"Missing required factors: {missing_factors}")
        
        # Check for negative weights
        negative_weights = [f for f, w in weights.items() if w < 0]
        if negative_weights:
            raise ValueError(f"Factor weights must be non-negative: {negative_weights}")
        
        # Check weights sum to 1.0
        if abs(sum(weights.values()) - 1.0) > 0.001:
            raise ValueError(f"Factor weights must sum to 1.0, got {sum(weights.values())}")
    
    def calculate_multi_factor_scores(self, stock_data: StockData, 
                                    market_data: Optional[StockData] = None) -> Dict[str, Any]:
        """Calculate scores from all factors"""
        # Calculate individual factor scores
        technical_score = self.technical_scorer.calculate_score(stock_data)
        fundamental_score = self.fundamental_scorer.calculate_score(stock_data)
        risk_score = self.risk_scorer.calculate_score(stock_data, market_data)
        market_context_score = self.market_context_analyzer.calculate_score(stock_data, market_data)
        
        # Package results
        factor_scores = {
            'technical': technical_score,
            'fundamental': fundamental_score,
            'risk': risk_score,
            'market_context': market_context_score
        }
        
        # Calculate composite score
        composite_score = self._calculate_composite_score(factor_scores)
        
        # Calculate weighted individual scores
        weighted_scores = {
            factor: score['score'] * self.factor_weights[factor] 
            for factor, score in factor_scores.items()
            if score['score'] is not None
        }
        
        return {
            **factor_scores,
            'composite_score': composite_score,
            'weighted_scores': weighted_scores
        }
    
    def _calculate_composite_score(self, factor_scores: Dict[str, Dict[str, Any]]) -> float:
        """Calculate weighted composite score"""
        weighted_sum = 0.0
        total_weight = 0.0
        
        for factor, score_data in factor_scores.items():
            if score_data['score'] is not None:
                weighted_sum += score_data['score'] * self.factor_weights[factor]
                total_weight += self.factor_weights[factor]
        
        if total_weight == 0:
            return 0.0
        
        # Normalize by actual weights used (in case some factors unavailable)
        return weighted_sum / total_weight if total_weight < 1.0 else weighted_sum
    
    def generate_enhanced_recommendation(self, stock_data: StockData,
                                       market_data: Optional[StockData] = None,
                                       risk_tolerance: str = 'moderate',
                                       time_horizon: str = 'medium') -> Dict[str, Any]:
        """Generate enhanced recommendation with all factors"""
        
        # Calculate multi-factor scores
        factor_scores = self.calculate_multi_factor_scores(stock_data, market_data)
        composite_score = factor_scores['composite_score']
        
        # Calculate enhanced confidence
        confidence_data = self.calculate_enhanced_confidence(factor_scores, stock_data)
        
        # Calculate advanced price targets
        price_targets = self.calculate_advanced_price_targets(
            stock_data, factor_scores, risk_tolerance
        )
        
        # Generate recommendation rationale
        rationale = self.generate_recommendation_rationale(factor_scores, composite_score)
        
        # Determine action based on composite score and confidence
        action = self._determine_action(composite_score, confidence_data['confidence'])
        
        # Apply risk tolerance adjustments
        risk_data = self._extract_risk_data(factor_scores)
        adjusted_recommendation = self._apply_risk_tolerance_adjustments(
            {
                'position_size': price_targets['position_size'],
                'stop_loss': price_targets['stop_loss']
            },
            risk_data,
            risk_tolerance
        )
        
        # Generate scenario analysis and risks/opportunities
        scenario_analysis = self._generate_scenario_analysis()
        key_risks = self._identify_key_risks(factor_scores, stock_data)
        key_opportunities = self._identify_key_opportunities(factor_scores, stock_data)
        
        return {
            'action': action,
            'confidence': confidence_data['confidence'],
            'confidence_interval': confidence_data['confidence_interval'],
            'target_price': price_targets['target_price'],
            'stop_loss': adjusted_recommendation['stop_loss'],
            'position_size': adjusted_recommendation['position_size'],
            'rationale': rationale,
            'factor_scores': factor_scores,
            'risk_level': self._classify_risk_level(risk_data),
            'time_horizon': time_horizon,
            'scenario_analysis': scenario_analysis,
            'key_risks': key_risks,
            'key_opportunities': key_opportunities
        }
    
    def analyze_stock_enhanced(self, stock_data: StockData,
                             risk_tolerance: str = 'moderate',
                             time_horizon: str = 'medium',
                             market_data: Optional[StockData] = None) -> Dict[str, Any]:
        """Full enhanced stock analysis pipeline"""
        
        # Get technical analysis
        technical_analysis = self.technical_indicators.get_indicator_summary(stock_data.data)
        
        # Get fundamental analysis
        try:
            fundamental_analysis = self.fundamental_analysis.get_fundamental_summary(stock_data)
        except Exception:
            fundamental_analysis = None
        
        # Generate enhanced recommendation
        enhanced_recommendation = self.generate_enhanced_recommendation(
            stock_data, market_data, risk_tolerance, time_horizon
        )
        
        # Get risk assessment
        from src.engine.decision_engine import RiskAssessment
        risk_assessor = RiskAssessment()
        
        try:
            risk_analysis = risk_assessor.assess_enhanced_risk(stock_data, market_data)
        except Exception:
            risk_analysis = risk_assessor.assess_risk(stock_data)
        
        # Package comprehensive analysis
        return {
            'symbol': stock_data.symbol,
            'enhanced_recommendation': enhanced_recommendation,
            'factor_breakdown': enhanced_recommendation['factor_scores'],
            'technical_analysis': technical_analysis,
            'fundamental_analysis': fundamental_analysis,
            'risk_analysis': risk_analysis,
            'market_context': self._get_market_context_summary(market_data)
        }
    
    def calculate_enhanced_confidence(self, factor_scores: Dict[str, Any], 
                                    stock_data: StockData) -> Dict[str, Any]:
        """Calculate enhanced confidence with intervals"""
        # Simple implementation for now
        individual_confidences = []
        data_quality_penalty = 0.0
        
        # Check data quality first
        if len(stock_data.data) < 50:
            data_quality_penalty = 0.3  # Significant penalty for insufficient data
        elif len(stock_data.data) < 100:
            data_quality_penalty = 0.15  # Moderate penalty for limited data
        
        for factor, score_data in factor_scores.items():
            if factor in ['technical', 'fundamental', 'risk', 'market_context'] and score_data['score'] is not None:
                # Higher absolute scores generally mean higher confidence
                factor_confidence = min(0.9, abs(score_data['score']) + 0.3)
                individual_confidences.append(factor_confidence)
        
        if not individual_confidences:
            base_confidence = 0.1
        else:
            base_confidence = sum(individual_confidences) / len(individual_confidences)
        
        # Apply data quality penalty
        base_confidence = max(0.1, base_confidence - data_quality_penalty)
        
        # Calculate confidence interval (simple approach)
        confidence_width = 0.15  # ±15%
        lower_bound = max(0.0, base_confidence - confidence_width)
        upper_bound = min(1.0, base_confidence + confidence_width)
        
        return {
            'confidence': base_confidence,
            'confidence_interval': (lower_bound, upper_bound)
        }
    
    def calculate_advanced_price_targets(self, stock_data: StockData, 
                                       factor_scores: Dict[str, Any],
                                       risk_tolerance: str) -> Dict[str, Any]:
        """Calculate advanced price targets"""
        current_price = stock_data.current_price
        composite_score = factor_scores['composite_score']
        
        # Simple target calculation based on composite score
        if composite_score > 0.3:
            target_multiplier = 1.0 + (composite_score * 0.2)  # Up to 20% upside
        elif composite_score < -0.3:
            target_multiplier = 1.0 + (composite_score * 0.15)  # Up to 15% downside
        else:
            target_multiplier = 1.0  # Neutral
        
        target_price = current_price * target_multiplier
        
        # Stop loss calculation
        risk_multipliers = {'conservative': 0.05, 'moderate': 0.08, 'aggressive': 0.12}
        stop_loss_distance = risk_multipliers.get(risk_tolerance, 0.08)
        
        if composite_score > 0:
            stop_loss = current_price * (1 - stop_loss_distance)
        else:
            stop_loss = current_price * (1 + stop_loss_distance)
        
        # Position sizing based on risk
        base_position = 0.05  # 5% base position
        risk_score = factor_scores.get('risk', {}).get('score', 0)
        
        # Reduce position size for higher risk
        if risk_score and risk_score < -0.3:
            position_size = base_position * 0.5
        elif risk_score and risk_score < -0.1:
            position_size = base_position * 0.75
        else:
            position_size = base_position
        
        return {
            'target_price': target_price,
            'stop_loss': stop_loss,
            'position_size': position_size
        }
    
    def _get_composite_direction(self, composite_score: float) -> str:
        """Helper to get a qualitative direction from the composite score."""
        if composite_score > 0.1:
            return 'positive'
        elif composite_score < -0.1:
            return 'negative'
        else:
            return 'neutral'

    def generate_recommendation_rationale(self, factor_scores: Dict[str, Any], 
                                        composite_score: float) -> Dict[str, Any]:
        """Generate rationale for the recommendation"""
        primary_factors = []
        supporting_factors = []
        
        # Identify primary contributing factors
        for factor, score_data in factor_scores.items():
            if factor in ['technical', 'fundamental', 'risk', 'market_context']:
                if score_data['score'] is not None and abs(score_data['score']) > 0.3:
                    if score_data['score'] > 0:
                        primary_factors.append(f"{factor} strength")
                    else:
                        primary_factors.append(f"{factor} weakness")
                elif score_data['score'] is not None and abs(score_data['score']) > 0.1:
                    supporting_factors.append(f"moderate {factor} signal")
        
        return {
            'primary_factors': primary_factors,
            'supporting_factors': supporting_factors,
            'composite_direction': self._get_composite_direction(composite_score)
        }
    
    def _determine_action(self, composite_score: float, confidence: float) -> str:
        """Determine buy/sell/hold action"""
        # Low confidence should default to HOLD
        if confidence < 0.4:
            return 'HOLD'
        
        # High confidence thresholds
        if composite_score > 0.3 and confidence > 0.6:
            return 'BUY'
        elif composite_score < -0.3 and confidence > 0.6:
            return 'SELL'
        else:
            return 'HOLD'
    
    def _get_time_horizon_weights(self, time_horizon: str) -> Dict[str, float]:
        """Get factor weights adjusted for time horizon"""
        if time_horizon == 'short':
            return {
                'technical': 0.60,
                'fundamental': 0.15,
                'risk': 0.20,
                'market_context': 0.05
            }
        elif time_horizon == 'long':
            return {
                'technical': 0.15,
                'fundamental': 0.55,
                'risk': 0.20,
                'market_context': 0.10
            }
        else:  # medium
            return {
                'technical': 0.35,
                'fundamental': 0.35,
                'risk': 0.20,
                'market_context': 0.10
            }
    
    def _apply_risk_tolerance_adjustments(self, base_recommendation: Dict[str, Any],
                                        risk_data: Dict[str, Any],
                                        risk_tolerance: str) -> Dict[str, Any]:
        """Apply risk tolerance adjustments"""
        # Get risk metrics
        var_95 = risk_data.get('var_95', 0)
        volatility = risk_data.get('volatility', 0.2)
        
        # Adjust position size based on risk tolerance
        position_size = base_recommendation['position_size']
        stop_loss = base_recommendation['stop_loss']
        
        if risk_tolerance == 'conservative':
            # For conservative investors, reduce position size for high-risk stocks
            var_95_abs = abs(var_95) if var_95 is not None else 0.0
            if var_95_abs > 0.05 or volatility > 0.3:
                position_size *= 0.5
            # Tighter stop losses
            if stop_loss < base_recommendation['stop_loss']:
                stop_loss = base_recommendation['stop_loss'] * 1.02  # 2% tighter
        elif risk_tolerance == 'aggressive':
            # Larger position sizes for high conviction
            var_95_abs = abs(var_95) if var_95 is not None else 0.0
            if var_95_abs < 0.03 and volatility < 0.25:
                position_size *= 1.5
            # Wider stop losses
            stop_loss = base_recommendation['stop_loss'] * 0.98  # 2% wider
        
        return {
            'position_size': min(position_size, 0.10),  # Cap at 10%
            'stop_loss': stop_loss
        }
    
    def _extract_risk_data(self, factor_scores: Dict[str, Any]) -> Dict[str, Any]:
        """Extract risk data from factor scores"""
        risk_score_data = factor_scores.get('risk', {})
        details = risk_score_data.get('details', {})
        
        return {
            'var_95': details.get('var_95', 0),
            'volatility': details.get('volatility', 0.2),
            'beta': details.get('beta', 1.0),
            'risk_score': risk_score_data.get('score', 0)
        }
    
    def _classify_risk_level(self, risk_data: Dict[str, Any]) -> str:
        """Classify overall risk level"""
        var_95_value = risk_data.get('var_95')
        var_95 = abs(var_95_value) if var_95_value is not None else 0.0
        volatility = risk_data.get('volatility', 0.2)
        
        if var_95 > 0.07 or volatility > 0.4:
            return 'HIGH'
        elif var_95 < 0.03 and volatility < 0.2:
            return 'LOW'
        else:
            return 'MEDIUM'
    
    def _generate_scenario_analysis(self) -> Dict[str, Any]:
        """Generate scenario analysis"""
        return {
            'bull_case': 'Strong technical and fundamental alignment',
            'bear_case': 'Risk factors and market headwinds',
            'base_case': 'Balanced outlook with moderate expectations'
        }
    
    def _identify_key_risks(self, factor_scores: Dict[str, Any], 
                          stock_data: StockData) -> List[str]:
        """Identify key risks"""
        risks = []
        
        # Check data quality
        if len(stock_data.data) < 100:
            risks.append('insufficient_data')
        
        # Check risk factors
        risk_details = factor_scores.get('risk', {}).get('details', {})
        var_95 = risk_details.get('var_95')
        if var_95 is not None and var_95 < -0.05:
            risks.append('high_downside_risk')
        
        if not risks:
            risks.append('market_volatility')
        
        return risks
    
    def _identify_key_opportunities(self, factor_scores: Dict[str, Any], 
                                  stock_data: StockData) -> List[str]:
        """Identify key opportunities"""
        opportunities = []
        
        # Check for positive signals (handle None scores)
        technical_score = factor_scores.get('technical', {}).get('score')
        if technical_score is not None and technical_score > 0.3:
            opportunities.append('technical_momentum')
        
        fundamental_score = factor_scores.get('fundamental', {}).get('score')
        if fundamental_score is not None and fundamental_score > 0.3:
            opportunities.append('fundamental_value')
        
        if not opportunities:
            opportunities.append('market_recovery')
        
        return opportunities
    
    def _get_market_context_summary(self, market_data: Optional[StockData]) -> Dict[str, Any]:
        """Get market context summary"""
        if market_data is None:
            return {'status': 'no_market_data'}
        
        return {
            'status': 'market_data_available',
            'market_trend': 'neutral'  # Simplified for now
        }