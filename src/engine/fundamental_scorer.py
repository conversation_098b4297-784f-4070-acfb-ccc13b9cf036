import yfinance as yf

from typing import Dict, Any, Optional
from src.data.yahoo_client import StockData
from src.analysis.fundamental import FundamentalAnalysis

class FundamentalScorer:
    """Enhanced fundamental analysis scorer with comprehensive financial metrics"""
    
    def __init__(self, risk_tolerance: str = 'moderate'):
        self.fundamental_analysis = FundamentalAnalysis()
        self.risk_tolerance = risk_tolerance.lower()
        self._setup_risk_thresholds()
    
    def _calculate_pe_score(self, pe_ratio: Optional[float]) -> Optional[float]:
        """Calculate P/E ratio score"""
        if not pe_ratio:
            return None
        
        if 10 <= pe_ratio <= 25:
            return 0.3
        elif pe_ratio < 10:
            return 0.1  # Might be value trap
        else:
            return -0.2  # Too expensive
    
    def _calculate_roe_score(self, roe: Optional[float]) -> Optional[float]:
        """Calculate ROE score"""
        if not roe:
            return None
        
        if roe > 15:
            return 0.4
        elif roe > 10:
            return 0.2
        else:
            return -0.1
    
    def _calculate_debt_score(self, debt_to_equity: Optional[float]) -> Optional[float]:
        """Calculate debt-to-equity score"""
        if not debt_to_equity:
            return None
        
        thresholds = self.thresholds['debt_to_equity']
        if debt_to_equity < thresholds['excellent']:
            return 0.2
        elif debt_to_equity < thresholds['good']:
            return 0.0
        else:
            return -0.3
    
    def _setup_risk_thresholds(self):
        """Setup risk-adjusted thresholds based on risk tolerance"""
        if self.risk_tolerance == 'conservative':
            self.thresholds = {
                'debt_to_equity': {'excellent': 0.2, 'good': 0.4},
                'current_ratio': {'excellent': 2.5, 'good': 1.8},
                'quick_ratio': {'excellent': 1.5, 'good': 1.0},
                'roa': {'excellent': 8, 'good': 5},
                'revenue_growth': {'excellent': 8, 'good': 3},
                'earnings_growth': {'excellent': 10, 'good': 5}
            }
        elif self.risk_tolerance == 'aggressive':
            self.thresholds = {
                'debt_to_equity': {'excellent': 0.5, 'good': 1.0},
                'current_ratio': {'excellent': 1.8, 'good': 1.2},
                'quick_ratio': {'excellent': 1.0, 'good': 0.7},
                'roa': {'excellent': 5, 'good': 2},
                'revenue_growth': {'excellent': 15, 'good': 8},
                'earnings_growth': {'excellent': 20, 'good': 10}
            }
        else:  # moderate
            self.thresholds = {
                'debt_to_equity': {'excellent': 0.3, 'good': 0.6},
                'current_ratio': {'excellent': 2.0, 'good': 1.5},
                'quick_ratio': {'excellent': 1.2, 'good': 0.8},
                'roa': {'excellent': 6, 'good': 3},
                'revenue_growth': {'excellent': 12, 'good': 5},
                'earnings_growth': {'excellent': 15, 'good': 7}
            }
    
    def _calculate_current_ratio_score(self, current_ratio: Optional[float]) -> Optional[float]:
        """Calculate current ratio score (liquidity strength)"""
        if not current_ratio:
            return None
        
        thresholds = self.thresholds['current_ratio']
        if current_ratio >= thresholds['excellent']:
            return 0.15
        elif current_ratio >= thresholds['good']:
            return 0.1
        elif current_ratio >= 1.0:
            return 0.0
        else:
            return -0.2  # Poor liquidity
    
    def _calculate_quick_ratio_score(self, quick_ratio: Optional[float]) -> Optional[float]:
        """Calculate quick ratio score (immediate liquidity)"""
        if not quick_ratio:
            return None
        
        thresholds = self.thresholds['quick_ratio']
        if quick_ratio >= thresholds['excellent']:
            return 0.1
        elif quick_ratio >= thresholds['good']:
            return 0.05
        elif quick_ratio >= 0.5:
            return 0.0
        else:
            return -0.15  # Very poor liquidity
    
    def _calculate_cash_to_debt_score(self, cash: Optional[float], total_debt: Optional[float]) -> Optional[float]:
        """Calculate cash-to-debt ratio score (financial stability)"""
        if not cash or not total_debt or total_debt <= 0:
            return None
        
        cash_to_debt = cash / total_debt
        if cash_to_debt >= 0.5:
            return 0.15  # Strong cash position
        elif cash_to_debt >= 0.25:
            return 0.1
        elif cash_to_debt >= 0.1:
            return 0.0
        else:
            return -0.1  # Weak cash position
    
    def _calculate_interest_coverage_score(self, ebitda: Optional[float], interest_expense: Optional[float]) -> Optional[float]:
        """Calculate interest coverage ratio score (debt service ability)"""
        if not ebitda or not interest_expense or interest_expense <= 0:
            return None
        
        coverage = ebitda / interest_expense
        if coverage >= 8:
            return 0.2  # Very safe
        elif coverage >= 4:
            return 0.15  # Safe
        elif coverage >= 2:
            return 0.05  # Adequate
        elif coverage >= 1.5:
            return -0.1  # Concerning
        else:
            return -0.25  # Dangerous
    
    def _calculate_roa_score(self, roa: Optional[float]) -> Optional[float]:
        """Calculate ROA (Return on Assets) score"""
        if not roa:
            return None
        
        thresholds = self.thresholds['roa']
        if roa >= thresholds['excellent']:
            return 0.25
        elif roa >= thresholds['good']:
            return 0.15
        elif roa >= 1:
            return 0.0
        else:
            return -0.1  # Poor asset utilization
    
    def _calculate_profit_margin_score(self, profit_margins: Dict[str, Optional[float]]) -> Optional[float]:
        """Calculate combined profit margins score"""
        gross_margin = profit_margins.get('gross_margin')
        operating_margin = profit_margins.get('operating_margin')
        net_margin = profit_margins.get('net_margin')

        if not any([gross_margin, operating_margin, net_margin]):
            return None

        score = 0.0
        score += self._score_gross_margin(gross_margin)
        score += self._score_operating_margin(operating_margin)
        score += self._score_net_margin(net_margin)

        return score

    def _score_gross_margin(self, gross_margin: Optional[float]) -> float:
        """Score gross margin component (weight: 0.3)"""
        if gross_margin is None:
            return 0.0

        if gross_margin >= 50:
            return 0.1
        elif gross_margin >= 30:
            return 0.05
        elif gross_margin < 15:
            return -0.05
        return 0.0

    def _score_operating_margin(self, operating_margin: Optional[float]) -> float:
        """Score operating margin component (weight: 0.4)"""
        if operating_margin is None:
            return 0.0

        if operating_margin >= 20:
            return 0.15
        elif operating_margin >= 10:
            return 0.1
        elif operating_margin >= 5:
            return 0.0
        else:
            return -0.1

    def _score_net_margin(self, net_margin: Optional[float]) -> float:
        """Score net margin component (weight: 0.3)"""
        if net_margin is None:
            return 0.0

        if net_margin >= 15:
            return 0.1
        elif net_margin >= 8:
            return 0.05
        elif net_margin < 2:
            return -0.05
        return 0.0
    
    def _calculate_ebitda_margin_score(self, ebitda_margins: Optional[float]) -> Optional[float]:
        """Calculate EBITDA margin score"""
        if not ebitda_margins:
            return None
        
        if ebitda_margins >= 25:
            return 0.15  # Excellent profitability
        elif ebitda_margins >= 15:
            return 0.1   # Good profitability
        elif ebitda_margins >= 8:
            return 0.05  # Adequate profitability
        elif ebitda_margins >= 3:
            return 0.0   # Marginal
        else:
            return -0.1  # Poor profitability
    
    def _calculate_growth_score(self, growth_metrics: Dict[str, Optional[float]]) -> Optional[float]:
        """Calculate combined growth score"""
        revenue_growth = growth_metrics.get('revenue_growth_yoy')
        earnings_growth = growth_metrics.get('earnings_growth_yoy')
        
        if not any([revenue_growth, earnings_growth]):
            return None
        
        score = 0.0
        
        # Revenue growth scoring (weight: 0.4)
        if revenue_growth is not None:
            thresholds = self.thresholds['revenue_growth']
            if revenue_growth >= thresholds['excellent']:
                score += 0.12
            elif revenue_growth >= thresholds['good']:
                score += 0.08
            elif revenue_growth >= 0:
                score += 0.0
            else:
                score -= 0.08  # Negative growth
        
        # Earnings growth scoring (weight: 0.6)
        if earnings_growth is not None:
            thresholds = self.thresholds['earnings_growth']
            if earnings_growth >= thresholds['excellent']:
                score += 0.18
            elif earnings_growth >= thresholds['good']:
                score += 0.12
            elif earnings_growth >= 0:
                score += 0.0
            else:
                score -= 0.12  # Negative earnings growth
        
        return score
    
    def _calculate_pb_ratio_score(self, pb_ratio: Optional[float]) -> Optional[float]:
        """Calculate Price-to-Book ratio score"""
        if not pb_ratio:
            return None
        
        if 0.5 <= pb_ratio <= 2.0:
            return 0.1   # Fair value range
        elif pb_ratio < 0.5:
            return -0.05  # Might be distressed
        elif pb_ratio <= 3.0:
            return 0.05   # Slightly expensive
        else:
            return -0.1   # Overvalued
    
    def _calculate_ev_ebitda_score(self, ev_ebitda: Optional[float]) -> Optional[float]:
        """Calculate Enterprise Value to EBITDA score"""
        if not ev_ebitda:
            return None
        
        if 8 <= ev_ebitda <= 15:
            return 0.1    # Fair value range
        elif ev_ebitda < 8:
            return 0.15   # Undervalued
        elif ev_ebitda <= 20:
            return 0.0    # Slightly expensive
        else:
            return -0.15  # Overvalued
    
    def _calculate_fcf_yield_score(self, market_cap: Optional[float], free_cash_flow: Optional[float]) -> Optional[float]:
        """Calculate Free Cash Flow Yield score"""
        if not market_cap or not free_cash_flow or market_cap <= 0:
            return None
        
        fcf_yield = (free_cash_flow / market_cap) * 100
        
        if fcf_yield >= 8:
            return 0.15   # Excellent cash generation
        elif fcf_yield >= 5:
            return 0.1    # Good cash generation
        elif fcf_yield >= 2:
            return 0.05   # Adequate cash generation
        elif fcf_yield >= 0:
            return 0.0    # Marginal
        else:
            return -0.1   # Negative free cash flow
    
    def _calculate_dividend_score(self, dividend_metrics: Dict[str, Optional[float]]) -> Optional[float]:
        """Calculate dividend sustainability score"""
        dividend_yield = dividend_metrics.get('dividend_yield')
        payout_ratio = dividend_metrics.get('payout_ratio')
        
        if not dividend_yield:
            return 0.0  # No dividend is neutral
        
        score = 0.0
        
        # Dividend yield scoring
        if 2 <= dividend_yield <= 6:
            score += 0.08  # Attractive yield
        elif dividend_yield > 8:
            score -= 0.05  # Potentially unsustainable
        
        # Payout ratio scoring (if available)
        if payout_ratio is not None:
            if payout_ratio <= 60:
                score += 0.05  # Sustainable payout
            elif payout_ratio <= 80:
                score += 0.0   # Moderate payout
            else:
                score -= 0.08  # High risk of dividend cut
        
        return score
    
    def _safe_get_financial_value(self, financial_data: Any, key: str, default: Optional[float] = None) -> Optional[float]:
        """Safely extract financial data value"""
        if financial_data is None:
            return default
        try:
            if hasattr(financial_data, 'get'):
                value = financial_data.get(key, default)
            else:
                return default
            return float(value) if value is not None and value != 0 else default
        except (TypeError, ValueError, KeyError):
            return default
    
    def _extract_financial_metrics(self, latest_balance: Any, latest_financials: Any) -> Dict[str, Optional[float]]:
        """Extract key financial metrics from statements"""
        metrics = {}
        
        # Balance sheet metrics
        if latest_balance is not None:
            metrics['total_debt'] = self._safe_get_financial_value(latest_balance, 'Total Debt')
            metrics['net_debt'] = self._safe_get_financial_value(latest_balance, 'Net Debt')  
            metrics['tangible_book_value'] = self._safe_get_financial_value(latest_balance, 'Tangible Book Value')
            metrics['cash_and_equivalents'] = self._safe_get_financial_value(latest_balance, 'Cash And Cash Equivalents')
            metrics['ordinary_shares'] = self._safe_get_financial_value(latest_balance, 'Ordinary Shares Number')
        
        # Income statement metrics
        if latest_financials is not None:
            metrics['total_revenue'] = self._safe_get_financial_value(latest_financials, 'Total Revenue')
            metrics['operating_revenue'] = self._safe_get_financial_value(latest_financials, 'Operating Revenue') 
            metrics['cost_of_revenue'] = self._safe_get_financial_value(latest_financials, 'Cost Of Revenue')
            metrics['operating_income'] = self._safe_get_financial_value(latest_financials, 'Operating Income')
            metrics['ebitda'] = self._safe_get_financial_value(latest_financials, 'EBITDA')
            metrics['interest_expense'] = self._safe_get_financial_value(latest_financials, 'Interest Expense')
            metrics['net_interest_income'] = self._safe_get_financial_value(latest_financials, 'Net Interest Income')
            metrics['diluted_eps'] = self._safe_get_financial_value(latest_financials, 'Diluted EPS')
            metrics['total_expenses'] = self._safe_get_financial_value(latest_financials, 'Total Expenses')
        
        return metrics
    
    def _calculate_net_cash_position_score(self, metrics: Dict[str, Optional[float]]) -> Optional[float]:
        """Calculate net cash position score"""
        cash = metrics.get('cash_and_equivalents')
        debt = metrics.get('total_debt')
        shares = metrics.get('ordinary_shares')
        
        if not all([cash, debt, shares]) or shares <= 0:
            return None
        
        net_cash_per_share = (cash - debt) / shares
        
        if net_cash_per_share > 50:  # Strong net cash position
            return 0.15
        elif net_cash_per_share > 20:
            return 0.1
        elif net_cash_per_share > 0:
            return 0.05
        elif net_cash_per_share > -20:
            return 0.0  # Manageable net debt
        else:
            return -0.1  # High debt burden
    
    def _calculate_tangible_book_value_score(self, metrics: Dict[str, Optional[float]], current_price: float) -> Optional[float]:
        """Calculate tangible book value score"""
        tbv = metrics.get('tangible_book_value')
        shares = metrics.get('ordinary_shares')
        
        if not all([tbv, shares]) or shares <= 0:
            return None
        
        tbv_per_share = tbv / shares
        price_to_tbv = current_price / tbv_per_share if tbv_per_share > 0 else None
        
        if not price_to_tbv:
            return None
        
        if price_to_tbv < 0.8:  # Trading below tangible book value
            return 0.2  # Potentially undervalued
        elif price_to_tbv < 1.2:  # Close to book value
            return 0.1
        elif price_to_tbv < 2.0:  # Reasonable premium
            return 0.0
        else:  # High premium to book value
            return -0.05
    
    def _calculate_cash_strength_score(self, metrics: Dict[str, Optional[float]]) -> Optional[float]:
        """Calculate cash per share strength score"""
        cash = metrics.get('cash_and_equivalents')
        shares = metrics.get('ordinary_shares')
        
        if not all([cash, shares]) or shares <= 0:
            return None
        
        cash_per_share = cash / shares
        
        if cash_per_share > 100:  # Very strong cash position
            return 0.1
        elif cash_per_share > 50:
            return 0.08
        elif cash_per_share > 20:
            return 0.05
        elif cash_per_share > 5:
            return 0.02
        else:
            return 0.0  # Low cash reserves
    
    def _calculate_revenue_per_share_score(self, metrics: Dict[str, Optional[float]]) -> Optional[float]:
        """Calculate revenue per share score"""
        revenue = metrics.get('total_revenue')
        shares = metrics.get('ordinary_shares')
        
        if not all([revenue, shares]) or shares <= 0:
            return None
        
        revenue_per_share = revenue / shares
        
        if revenue_per_share > 1000:  # High revenue per share
            return 0.1
        elif revenue_per_share > 500:
            return 0.08
        elif revenue_per_share > 200:
            return 0.05
        elif revenue_per_share > 50:
            return 0.02
        else:
            return 0.0  # Low revenue generation
    
    def _calculate_cost_efficiency_score(self, metrics: Dict[str, Optional[float]]) -> Optional[float]:
        """Calculate cost efficiency score"""
        revenue = metrics.get('total_revenue')
        cost_of_revenue = metrics.get('cost_of_revenue')
        
        if not all([revenue, cost_of_revenue]) or revenue <= 0:
            return None
        
        cost_ratio = cost_of_revenue / revenue
        
        if cost_ratio < 0.5:  # Very efficient operations
            return 0.15
        elif cost_ratio < 0.65:  # Good efficiency
            return 0.1
        elif cost_ratio < 0.8:  # Moderate efficiency  
            return 0.05
        elif cost_ratio < 0.9:  # Below average
            return 0.0
        else:  # Poor cost control
            return -0.1
    
    def _calculate_operating_leverage_score(self, metrics: Dict[str, Optional[float]]) -> Optional[float]:
        """Calculate operating leverage score"""
        operating_income = metrics.get('operating_income')
        revenue = metrics.get('total_revenue')
        
        if not all([operating_income, revenue]) or revenue <= 0:
            return None
        
        operating_margin = operating_income / revenue
        
        if operating_margin > 0.25:  # Excellent operating efficiency
            return 0.15
        elif operating_margin > 0.15:  # Strong operations
            return 0.1
        elif operating_margin > 0.08:  # Good operations
            return 0.05
        elif operating_margin > 0.02:  # Marginal
            return 0.0
        else:  # Poor or negative operating income
            return -0.1
    
    def _calculate_precise_interest_coverage_score(self, metrics: Dict[str, Optional[float]]) -> Optional[float]:
        """Calculate precise interest coverage score from financial statements"""
        ebitda = metrics.get('ebitda')
        interest_expense = metrics.get('interest_expense')
        
        if not all([ebitda, interest_expense]) or interest_expense <= 0:
            return None
        
        coverage_ratio = ebitda / interest_expense
        
        if coverage_ratio >= 15:  # Very safe
            return 0.2
        elif coverage_ratio >= 8:  # Safe
            return 0.15
        elif coverage_ratio >= 4:  # Adequate
            return 0.1
        elif coverage_ratio >= 2:  # Concerning
            return 0.0
        elif coverage_ratio >= 1.5:  # Risky
            return -0.1
        else:  # Dangerous
            return -0.2
    
    def _calculate_net_interest_impact_score(self, metrics: Dict[str, Optional[float]]) -> Optional[float]:
        """Calculate net interest income impact score"""
        net_interest = metrics.get('net_interest_income')
        revenue = metrics.get('total_revenue')
        
        if not all([net_interest, revenue]) or revenue <= 0:
            return None
        
        net_interest_margin = net_interest / revenue
        
        if net_interest_margin > 0.05:  # Strong net interest income
            return 0.1
        elif net_interest_margin > 0.02:  # Positive contribution
            return 0.05
        elif net_interest_margin > -0.02:  # Neutral
            return 0.0
        elif net_interest_margin > -0.05:  # Moderate burden
            return -0.05
        else:  # High interest burden
            return -0.1
    
    def _calculate_expense_management_score(self, metrics: Dict[str, Optional[float]]) -> Optional[float]:
        """Calculate expense management efficiency score"""
        total_expenses = metrics.get('total_expenses')
        revenue = metrics.get('total_revenue')
        
        if not all([total_expenses, revenue]) or revenue <= 0:
            return None
        
        expense_ratio = total_expenses / revenue
        
        if expense_ratio < 0.7:  # Very efficient expense management
            return 0.12
        elif expense_ratio < 0.85:  # Good expense control
            return 0.08
        elif expense_ratio < 0.95:  # Moderate expense management
            return 0.04
        elif expense_ratio < 1.0:  # Tight margins
            return 0.0
        else:  # Expenses exceed revenue (loss)
            return -0.15

    def calculate_score(self, stock_data: StockData) -> Dict[str, Any]:
        """Calculate comprehensive fundamental analysis score"""
        print(f"Starting fundamental analysis for {stock_data.symbol}")
        try:
            fundamental_data = self.fundamental_analysis.get_fundamental_summary(stock_data)
            if not fundamental_data or all(v is None for v in fundamental_data.values()):
                print("Insufficient fundamental data available")
                return self._create_insufficient_data_response()

            # Get additional financial data
            ticker_data = self._get_ticker_data(stock_data.symbol)
            if not ticker_data:
                return self._create_insufficient_data_response()

            # Extract financial metrics
            financial_metrics = self._extract_financial_metrics(
                ticker_data['latest_balance'],
                ticker_data['latest_financials']
            )

            # Calculate all component scores
            components = self._calculate_all_component_scores(
                fundamental_data,
                ticker_data,
                financial_metrics,
                stock_data
            )

            # Calculate final score
            score = sum(components.values())
            score = max(-1.0, min(1.0, score))  # Normalize to [-1, 1] range
            print(f"Final normalized score: {score}")

            return {
                'score': score,
                'details': {
                    'fundamental_data': fundamental_data,
                    'data_quality': 'sufficient',
                    'risk_tolerance': self.risk_tolerance,
                    'components_used': len(components)
                },
                'component_scores': components
            }

        except Exception as e:
            print(f"Error in fundamental analysis: {str(e)}")
            return {
                'score': None,
                'details': {
                    'error': str(e),
                    'data_quality': 'insufficient'
                },
                'component_scores': {}
            }

    def _create_insufficient_data_response(self) -> Dict[str, Any]:
        """Create response for insufficient data scenarios"""
        return {
            'score': None,
            'details': {'data_quality': 'insufficient'},
            'component_scores': {}
        }

    def _get_ticker_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get and extract ticker data from Yahoo Finance"""
        try:
            print(f"Fetching additional financial data for {symbol}")
            ticker = yf.Ticker(symbol)
            ticker_info = ticker.info

            # Extract financial statement data
            print("Extracting financial statements...")
            cash_flow = ticker.cashflow
            balance_sheet = ticker.balance_sheet
            financials = ticker.financials

            # Get latest financial data (most recent column)
            latest_cashflow = cash_flow.iloc[:, 0] if not cash_flow.empty else None
            latest_balance = balance_sheet.iloc[:, 0] if not balance_sheet.empty else None
            latest_financials = financials.iloc[:, 0] if not financials.empty else None

            free_cash_flow = latest_cashflow.get('Free Cash Flow') if latest_cashflow is not None else None
            total_cash = ticker_info.get('totalCash')
            ebitda = ticker_info.get('ebitda')
            interest_expense = ticker_info.get('interestExpense')
            print(f"Financial data extracted - FCF: {free_cash_flow}, Cash: {total_cash}, EBITDA: {ebitda}")

            return {
                'ticker_info': ticker_info,
                'latest_cashflow': latest_cashflow,
                'latest_balance': latest_balance,
                'latest_financials': latest_financials,
                'free_cash_flow': free_cash_flow,
                'total_cash': total_cash,
                'ebitda': ebitda,
                'interest_expense': interest_expense
            }
        except Exception as e:
            print(f"Error extracting financial statements: {str(e)}")
            return None

    def _calculate_all_component_scores(self, fundamental_data: Dict[str, Any],
                                      ticker_data: Dict[str, Any],
                                      financial_metrics: Dict[str, Any],
                                      stock_data: StockData) -> Dict[str, float]:
        """Calculate all component scores and return as dictionary"""
        components = {}

        # Calculate core valuation metrics
        self._add_valuation_scores(components, fundamental_data)

        # Calculate profitability metrics
        self._add_profitability_scores(components, fundamental_data, ticker_data)

        # Calculate financial health metrics
        self._add_financial_health_scores(components, fundamental_data, ticker_data)

        # Calculate growth and cash flow metrics
        self._add_growth_and_cashflow_scores(components, fundamental_data, ticker_data)

        # Calculate enhanced financial statement metrics
        self._add_enhanced_financial_scores(components, financial_metrics, stock_data)

        return components

    def _add_valuation_scores(self, components: Dict[str, float], fundamental_data: Dict[str, Any]) -> None:
        """Add core valuation metric scores"""
        print("Calculating core valuation metrics...")

        pe_score = self._calculate_pe_score(fundamental_data.get('pe_ratio'))
        if pe_score is not None:
            components['pe_ratio'] = pe_score
            print(f"PE Score: {pe_score}")

        pb_score = self._calculate_pb_ratio_score(fundamental_data.get('pb_ratio'))
        if pb_score is not None:
            components['pb_ratio'] = pb_score
            print(f"PB Score: {pb_score}")

        ev_ebitda_score = self._calculate_ev_ebitda_score(fundamental_data.get('enterprise_to_ebitda'))
        if ev_ebitda_score is not None:
            components['ev_ebitda'] = ev_ebitda_score
            print(f"EV/EBITDA Score: {ev_ebitda_score}")

    def _add_profitability_scores(self, components: Dict[str, float],
                                fundamental_data: Dict[str, Any],
                                ticker_data: Dict[str, Any]) -> None:
        """Add profitability metric scores"""
        print("Calculating profitability metrics...")

        roe_score = self._calculate_roe_score(fundamental_data.get('roe'))
        if roe_score is not None:
            components['roe'] = roe_score
            print(f"ROE Score: {roe_score}")

        roa_score = self._calculate_roa_score(fundamental_data.get('roa'))
        if roa_score is not None:
            components['roa'] = roa_score
            print(f"ROA Score: {roa_score}")

        # Profit margins
        print("Calculating profit margins...")
        ticker_info = ticker_data['ticker_info']
        profit_margins = {
            'gross_margin': ticker_info.get('grossMargins') * 100 if ticker_info.get('grossMargins') is not None else None,
            'operating_margin': ticker_info.get('operatingMargins') * 100 if ticker_info.get('operatingMargins') is not None else None,
            'net_margin': ticker_info.get('profitMargins') * 100 if ticker_info.get('profitMargins') is not None else None
        }
        print(f"Profit margins: {profit_margins}")
        margin_score = self._calculate_profit_margin_score(profit_margins)
        if margin_score is not None:
            components['profit_margins'] = margin_score
            print(f"Margin Score: {margin_score}")

        ebitda_margin_score = self._calculate_ebitda_margin_score(
            ticker_info.get('ebitdaMargins') * 100 if ticker_info.get('ebitdaMargins') is not None else None
        )
        if ebitda_margin_score is not None:
            components['ebitda_margin'] = ebitda_margin_score
            print(f"EBITDA Margin Score: {ebitda_margin_score}")

    def _add_financial_health_scores(self, components: Dict[str, float],
                                   fundamental_data: Dict[str, Any],
                                   ticker_data: Dict[str, Any]) -> None:
        """Add financial health metric scores"""
        print("Calculating financial health metrics...")
        ticker_info = ticker_data['ticker_info']

        debt_score = self._calculate_debt_score(fundamental_data.get('debt_to_equity'))
        if debt_score is not None:
            components['debt_to_equity'] = debt_score
            print(f"Debt Score: {debt_score}")

        current_ratio_score = self._calculate_current_ratio_score(fundamental_data.get('current_ratio'))
        if current_ratio_score is not None:
            components['current_ratio'] = current_ratio_score
            print(f"Current Ratio Score: {current_ratio_score}")

        quick_ratio_score = self._calculate_quick_ratio_score(fundamental_data.get('quick_ratio'))
        if quick_ratio_score is not None:
            components['quick_ratio'] = quick_ratio_score
            print(f"Quick Ratio Score: {quick_ratio_score}")

        cash_debt_score = self._calculate_cash_to_debt_score(
            ticker_data['total_cash'],
            ticker_info.get('totalDebt')
        )
        if cash_debt_score is not None:
            components['cash_to_debt'] = cash_debt_score
            print(f"Cash/Debt Score: {cash_debt_score}")

        interest_coverage_score = self._calculate_interest_coverage_score(
            ticker_data['ebitda'],
            ticker_data['interest_expense']
        )
        if interest_coverage_score is not None:
            components['interest_coverage'] = interest_coverage_score
            print(f"Interest Coverage Score: {interest_coverage_score}")

    def _add_growth_and_cashflow_scores(self, components: Dict[str, float],
                                      fundamental_data: Dict[str, Any],
                                      ticker_data: Dict[str, Any]) -> None:
        """Add growth and cash flow metric scores"""
        # Growth metrics
        print("Calculating growth metrics...")
        growth_metrics = {
            'revenue_growth_yoy': fundamental_data.get('revenue_growth_yoy'),
            'earnings_growth_yoy': fundamental_data.get('earnings_growth_yoy')
        }
        growth_score = self._calculate_growth_score(growth_metrics)
        if growth_score is not None:
            components['growth'] = growth_score
            print(f"Growth Score: {growth_score}")

        # Cash flow metrics
        print("Calculating cash flow metrics...")
        fcf_score = self._calculate_fcf_yield_score(
            fundamental_data.get('market_cap'),
            ticker_data['free_cash_flow']
        )
        if fcf_score is not None:
            components['fcf_yield'] = fcf_score
            print(f"FCF Yield Score: {fcf_score}")

        # Dividend metrics
        print("Calculating dividend metrics...")
        dividend_metrics = {
            'dividend_yield': fundamental_data.get('dividend_yield'),
            'payout_ratio': fundamental_data.get('payout_ratio')
        }
        dividend_score = self._calculate_dividend_score(dividend_metrics)
        if dividend_score is not None:
            components['dividend'] = dividend_score
            print(f"Dividend Score: {dividend_score}")

    def _add_enhanced_financial_scores(self, components: Dict[str, float],
                                     financial_metrics: Dict[str, Any],
                                     stock_data: StockData) -> None:
        """Add enhanced financial statement metric scores"""
        print("Calculating enhanced financial metrics...")

        # Define score calculations with their names for easier management
        score_calculations = [
            ('net_cash_position', self._calculate_net_cash_position_score, [financial_metrics]),
            ('tangible_book_value', self._calculate_tangible_book_value_score, [financial_metrics, stock_data.current_price]),
            ('cash_strength', self._calculate_cash_strength_score, [financial_metrics]),
            ('revenue_per_share', self._calculate_revenue_per_share_score, [financial_metrics]),
            ('cost_efficiency', self._calculate_cost_efficiency_score, [financial_metrics]),
            ('operating_leverage', self._calculate_operating_leverage_score, [financial_metrics]),
            ('precise_interest_coverage', self._calculate_precise_interest_coverage_score, [financial_metrics]),
            ('net_interest_impact', self._calculate_net_interest_impact_score, [financial_metrics]),
            ('expense_management', self._calculate_expense_management_score, [financial_metrics])
        ]

        # Calculate each score
        for score_name, score_func, args in score_calculations:
            try:
                score = score_func(*args)
                if score is not None:
                    components[score_name] = score
                    print(f"{score_name.replace('_', ' ').title()} Score: {score}")
            except Exception as e:
                print(f"Error calculating {score_name}: {str(e)}")