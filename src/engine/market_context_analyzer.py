from typing import Dict, Any, Optional
from src.data.yahoo_client import StockData

class MarketContextAnalyzer:
    """Market context analysis for enhanced recommendations"""
    
    def __init__(self):
        pass
    
    def calculate_score(self, stock_data: StockData, market_data: Optional[StockData] = None) -> Dict[str, Any]:
        """Calculate market context score"""
        try:
            if market_data is None:
                # Without market data, return neutral score
                return {
                    'score': 0.0,
                    'details': {
                        'market_data_available': False,
                        'sector_performance': 'unknown',
                        'market_regime': 'unknown'
                    },
                    'component_scores': {}
                }
            
            # Simple market context analysis
            stock_returns = stock_data.data['Close'].pct_change().dropna()
            market_returns = market_data.data['Close'].pct_change().dropna()
            
            # Calculate relative performance
            if len(stock_returns) > 30 and len(market_returns) > 30:
                # Use last 30 days for comparison
                stock_performance = stock_returns.iloc[-30:].sum()
                market_performance = market_returns.iloc[-30:].sum()
                
                relative_performance = stock_performance - market_performance
                
                # Score based on relative performance
                if relative_performance > 0.05:  # Outperforming by 5%
                    score = 0.4
                    sector_performance = 'outperform'
                elif relative_performance < -0.05:  # Underperforming by 5%
                    score = -0.3
                    sector_performance = 'underperform'
                else:
                    score = 0.1
                    sector_performance = 'inline'
                
                # Simple market trend detection
                recent_market_trend = market_returns.iloc[-10:].mean()
                if recent_market_trend > 0.001:  # 0.1% daily average
                    market_regime = 'bullish'
                elif recent_market_trend < -0.001:
                    market_regime = 'bearish'
                else:
                    market_regime = 'neutral'
            else:
                score = 0.0
                sector_performance = 'insufficient_data'
                market_regime = 'unknown'
            
            return {
                'score': max(-1.0, min(1.0, score)),
                'details': {
                    'market_data_available': True,
                    'sector_performance': sector_performance,
                    'market_regime': market_regime,
                    'relative_performance': relative_performance if 'relative_performance' in locals() else None
                },
                'component_scores': {
                    'relative_performance_score': score
                }
            }
            
        except Exception as e:
            return {
                'score': 0.0,
                'details': {
                    'error': str(e),
                    'market_data_available': market_data is not None,
                    'sector_performance': 'error',
                    'market_regime': 'unknown'
                },
                'component_scores': {}
            }
    
    def get_market_context(self, market_data: Optional[StockData] = None) -> Dict[str, Any]:
        """Get comprehensive market context"""
        if market_data is None:
            return {
                'market_regime': 'unknown',
                'volatility_regime': 'unknown',
                'sector_performance': 'unknown'
            }
        
        # Simple market context analysis
        returns = market_data.data['Close'].pct_change().dropna()
        
        if len(returns) < 30:
            return {
                'market_regime': 'insufficient_data',
                'volatility_regime': 'unknown',
                'sector_performance': 'unknown'
            }
        
        # Calculate volatility
        volatility = returns.std() * (252 ** 0.5)  # Annualized
        
        # Determine regimes
        recent_performance = returns.iloc[-20:].mean()
        
        if recent_performance > 0.002:
            market_regime = 'bullish'
        elif recent_performance < -0.002:
            market_regime = 'bearish'
        else:
            market_regime = 'neutral'
        
        if volatility > 0.3:
            volatility_regime = 'high'
        elif volatility > 0.2:
            volatility_regime = 'normal'
        else:
            volatility_regime = 'low'
        
        return {
            'market_regime': market_regime,
            'volatility_regime': volatility_regime,
            'sector_performance': 'normal'  # Placeholder
        }