from typing import Dict, Any, Optional
from src.data.yahoo_client import StockData
from src.engine.decision_engine import RiskAssessment

class RiskScorer:
    """Risk analysis scorer for enhanced recommendations"""
    
    def __init__(self):
        self.risk_assessment = RiskAssessment()
    
    def _get_risk_metrics(self, stock_data: StockData, market_data: Optional[StockData]) -> Dict[str, Any]:
        """Get risk assessment metrics"""
        if market_data:
            return self.risk_assessment.assess_enhanced_risk(stock_data, market_data)
        else:
            return self.risk_assessment.assess_risk(stock_data)
    
    def _extract_metrics(self, risk_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Extract key metrics from risk assessment"""
        volatility = risk_metrics.get('volatility', 0.2)
        max_drawdown = risk_metrics.get('max_drawdown', -0.1)
        sharpe_ratio = risk_metrics.get('sharpe_ratio', 0)
        
        var_95 = None
        if 'var_95' in risk_metrics:
            var_95 = risk_metrics['var_95'].get('var_percentage', 0)
        
        beta = 1.0
        if 'enhanced_beta' in risk_metrics:
            beta = risk_metrics['enhanced_beta'].get('beta', 1.0)
            
        return {
            'volatility': volatility,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'var_95': var_95,
            'beta': beta
        }
    
    def _calculate_volatility_score(self, volatility: float) -> float:
        """Calculate volatility score"""
        if volatility < 0.2:
            return 0.3
        elif volatility < 0.3:
            return 0.1
        else:
            return -0.4
    
    def _calculate_drawdown_score(self, max_drawdown: float) -> float:
        """Calculate drawdown score"""
        if abs(max_drawdown) < 0.1:
            return 0.2
        elif abs(max_drawdown) < 0.2:
            return 0.0
        else:
            return -0.3
    
    def _calculate_sharpe_score(self, sharpe_ratio: float) -> float:
        """Calculate Sharpe ratio score"""
        if sharpe_ratio > 1.0:
            return 0.3
        elif sharpe_ratio > 0.5:
            return 0.1
        else:
            return -0.2
    
    def _calculate_var_score(self, var_95: Optional[float]) -> float:
        """Calculate VaR score"""
        if not var_95:
            return 0.0
        
        if var_95 > -0.03:
            return 0.2
        elif var_95 > -0.05:
            return 0.0
        else:
            return -0.3
    
    def calculate_score(self, stock_data: StockData, market_data: Optional[StockData] = None) -> Dict[str, Any]:
        """Calculate risk-adjusted score"""
        try:
            risk_metrics = self._get_risk_metrics(stock_data, market_data)
            metrics = self._extract_metrics(risk_metrics)
            
            vol_score = self._calculate_volatility_score(metrics['volatility'])
            dd_score = self._calculate_drawdown_score(metrics['max_drawdown'])
            sharpe_score = self._calculate_sharpe_score(metrics['sharpe_ratio'])
            var_score = self._calculate_var_score(metrics['var_95'])
            
            score = vol_score + dd_score + sharpe_score + var_score
            score = max(-1.0, min(1.0, score))
            
            return {
                'score': score,
                'details': {
                    'volatility': metrics['volatility'],
                    'max_drawdown': metrics['max_drawdown'],
                    'sharpe_ratio': metrics['sharpe_ratio'],
                    'var_95': metrics['var_95'],
                    'beta': metrics['beta'],
                    'risk_classification': risk_metrics.get('risk_level', 'MEDIUM')
                },
                'component_scores': {
                    'volatility_score': vol_score,
                    'drawdown_score': dd_score,
                    'sharpe_score': sharpe_score,
                    'var_score': var_score
                }
            }
            
        except Exception as e:
            return {
                'score': None,
                'details': {
                    'error': str(e),
                    'risk_classification': 'UNKNOWN'
                },
                'component_scores': {}
            }