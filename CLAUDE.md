# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Running the Application
```bash
# Run analysis without installation
python main.py analyze RELIANCE.BO

# With options
python main.py analyze RELIANCE.BO --period 1y --risk moderate --format table --enhanced

# Install as package (optional)
pip install -e .
stock-analyzer analyze RELIANCE.BO
```

### Testing
```bash
# Run all tests
python -m pytest

# Run specific test suites
python -m pytest tests/unit/ -v
python -m pytest tests/cli/ -v
python -m pytest tests/integration/ -v

# Run specific test files
python -m pytest tests/unit/test_indicators.py -v
python -m pytest tests/unit/test_formatters.py -v
python -m pytest tests/unit/test_enhanced_fundamental_scorer.py -v

# Run with coverage
python -m pytest --cov=src tests/

# Run tests with specific markers
python -m pytest -m "not slow"
python -m pytest -m integration

# Run single test method
python -m pytest tests/unit/test_indicators.py::TestTechnicalIndicators::test_rsi_calculation -v
```

### Dependencies
```bash
# Install dependencies
pip install -r requirements.txt

# Install development dependencies (includes pytest, pytest-cov)
pip install -e .
```

## Architecture Overview

### Core Components

**CLI Layer** (`src/cli/`)
- `commands.py`: Click-based CLI interface with analyze command
- `formatters.py`: Output formatting (table, JSON, CSV) with error handling

**Data Layer** (`src/data/`)
- `yahoo_client.py`: Yahoo Finance API client for BSE stock data retrieval

**Analysis Layer** (`src/analysis/`)
- `indicators.py`: Technical indicators (RSI, SMA, Bollinger Bands, MACD, etc.)
- `fundamental.py`: Fundamental analysis calculations

**Engine Layer** (`src/engine/`)
- `decision_engine.py`: Main orchestration engine for stock analysis
- `enhanced_recommendation_engine.py`: Multi-factor recommendation system with weighted scoring
- `technical_scorer.py`: Technical analysis scoring component
- `fundamental_scorer.py`: Enhanced fundamental analysis scorer with 19+ comprehensive financial metrics
- `risk_scorer.py`: Risk assessment scoring
- `market_context_analyzer.py`: Market context analysis

### Data Flow
1. CLI receives user input (stock symbol, parameters)
2. YahooClient fetches stock data from Yahoo Finance
3. DecisionEngine orchestrates analysis through multiple scorers
4. Technical/Fundamental/Risk analysis components process data
5. FundamentalScorer extracts financial statements (balance sheet, income statement, cash flow) for comprehensive analysis
6. EnhancedRecommendationEngine aggregates scores with weighted factors
7. Results formatted and output via CLI formatters with categorized fundamental breakdown

### Key Design Patterns
- **Strategy Pattern**: Multiple formatters (table, JSON, CSV) implementing common interface
- **Engine Pattern**: DecisionEngine orchestrates multiple analysis components
- **Scorer Pattern**: Separate scorers for technical, fundamental, risk, and market context analysis
- **Weighted Scoring**: EnhancedRecommendationEngine combines multiple factors with configurable weights
- **Component Scoring**: FundamentalScorer breaks analysis into 19+ individual components across categories

### BSE Stock Symbols
- All symbols use `.BO` suffix (e.g., `RELIANCE.BO`, `TCS.BO`, `INFY.BO`)
- Symbol validation is case-insensitive but normalized to uppercase

### Risk Tolerance Levels
- `conservative`: Lower risk appetite, more stringent thresholds
- `moderate`: Balanced approach (default)
- `aggressive`: Higher risk tolerance for potentially higher returns

### Time Periods
- Supported periods: `1m`, `3m`, `6m`, `1y`, `2y`
- Default analysis period is `1y`

## Enhanced Fundamental Analysis Architecture

### Component Categories
The FundamentalScorer analyzes 19+ financial metrics across four categories:

1. **Valuation Metrics**: P/E ratio, P/B ratio, EV/EBITDA
2. **Profitability Metrics**: ROE, ROA, profit margins, EBITDA margins, operating leverage
3. **Financial Health Metrics**: Debt-to-equity, current ratio, quick ratio, cash-to-debt, interest coverage  
4. **Financial Statement Analysis**: Net cash position, tangible book value, cash strength, revenue per share, cost efficiency, precise interest coverage from statements, net interest impact, expense management

### Risk-Adjusted Scoring
- **Conservative**: Stricter thresholds (debt-to-equity < 0.2 excellent, current ratio > 2.5 excellent)
- **Moderate**: Balanced thresholds (debt-to-equity < 0.3 excellent, current ratio > 2.0 excellent) 
- **Aggressive**: Lenient thresholds (debt-to-equity < 0.5 excellent, current ratio > 1.8 excellent)

### Financial Statement Integration
- Extracts latest balance sheet, income statement, and cash flow data via yfinance
- Performs safe data extraction with comprehensive error handling
- Calculates precise metrics from actual financial statements rather than derived ratios
- Components are scored individually and aggregated into final fundamental score normalized to [-1, 1] range

### Display Architecture
The TerminalFormatter organizes component scores by category with impact descriptions:
- Shows top 3 metrics per category sorted by absolute score impact
- Provides qualitative descriptions (Strong/Good/Fair/Poor/Weak/Neutral)
- Displays risk tolerance and total components analyzed
- Maintains backward compatibility with existing analysis modes