{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(source:*)", "Bash(pip install:*)", "Bash(tree:*)", "Bash(find:*)", "Bash(python -m pytest --version)", "Bash(python -m pytest tests/ -v)", "<PERSON><PERSON>(python:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(python -m pytest tests/unit/test_indicators.py -v)", "Bash(python -m pytest tests/unit/test_indicators.py::TestTechnicalIndicators::test_rsi_calculation -v)", "Bash(python -m pytest tests/unit/ -v)", "<PERSON><PERSON>(mkdir:*)", "Bash(python -m pytest tests/unit/test_formatters.py::TestCSVFormatter::test_csv_special_character_escaping -v)", "Bash(python -m pytest tests/unit/test_formatters.py::TestUtilityFunctions::test_format_currency -v)", "Bash(python -m pytest tests/unit/test_formatters.py::TestTerminalFormatter::test_basic_terminal_formatting -v)", "Bash(python -m pytest tests/cli/ -v)", "Bash(python -m pytest tests/cli/test_commands.py::TestStockAnalyzeCommand::test_basic_stock_analysis -v)", "Bash(python -m pytest tests/cli/test_commands.py::TestParameterValidation::test_case_insensitive_symbol -v)", "Bash(python -m pytest tests/unit/ --tb=no --quiet)", "Bash(python -m pytest tests/unit/test_formatters.py::TestTerminalFormatter::test_detailed_vs_basic_formatting -v)", "<PERSON><PERSON>(python:*)", "Bash(pip install:*)", "<PERSON><PERSON>(stock-analyzer:*)", "mcp__ide__getDiagnostics", "mcp__sonarqube__system_ping", "mcp__sonarqube__system_status", "mcp__sonarqube__projects", "mcp__sonarqube__components", "mcp__sonarqube__issues"], "deny": []}}