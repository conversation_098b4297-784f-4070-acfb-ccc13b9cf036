import pytest
import pandas as pd
import numpy as np
from src.analysis.indicators import TechnicalIndicators

class TestTechnicalIndicators:
    
    @pytest.fixture
    def indicators(self):
        return TechnicalIndicators()
    
    def test_indicators_initialization(self, indicators):
        """Test that TechnicalIndicators initializes properly"""
        assert indicators is not None
        assert hasattr(indicators, 'calculate_sma')
        assert hasattr(indicators, 'calculate_rsi')
        assert hasattr(indicators, 'calculate_macd')
    
    def test_simple_moving_average(self, indicators, sample_stock_data):
        """Test Simple Moving Average calculation"""
        sma_20 = indicators.calculate_sma(sample_stock_data, window=20)
        
        assert isinstance(sma_20, pd.Series)
        assert len(sma_20) == len(sample_stock_data)
        # First 19 values should be NaN
        assert pd.isna(sma_20.iloc[:19]).all()
        # 20th value should not be NaN
        assert not pd.isna(sma_20.iloc[19])
        
        # Test different window sizes
        sma_50 = indicators.calculate_sma(sample_stock_data, window=50)
        assert pd.isna(sma_50.iloc[:49]).all()
        assert not pd.isna(sma_50.iloc[49])
    
    def test_sma_with_insufficient_data(self, indicators):
        """Test SMA with insufficient data points"""
        # Create small dataset
        small_data = pd.DataFrame({
            'Close': [100, 101, 102, 103, 104]
        })
        
        sma_10 = indicators.calculate_sma(small_data, window=10)
        assert pd.isna(sma_10).all()  # All values should be NaN
    
    def test_rsi_calculation(self, indicators, sample_stock_data):
        """Test RSI (Relative Strength Index) calculation"""
        rsi = indicators.calculate_rsi(sample_stock_data, window=14)
        
        assert isinstance(rsi, pd.Series)
        assert len(rsi) == len(sample_stock_data)
        # First 14 values should be NaN
        assert pd.isna(rsi.iloc[:14]).all()
        
        # RSI should be between 0 and 100
        valid_rsi = rsi.dropna()
        assert (valid_rsi >= 0).all()
        assert (valid_rsi <= 100).all()
    
    def test_rsi_extreme_values(self, indicators):
        """Test RSI with extreme price movements"""
        # Create data with only increasing prices
        increasing_data = pd.DataFrame({
            'Close': list(range(100, 150))
        })
        
        rsi = indicators.calculate_rsi(increasing_data, window=14)
        valid_rsi = rsi.dropna()
        # RSI should approach 100 for consistently increasing prices
        assert valid_rsi.iloc[-1] > 80
        
        # Create data with only decreasing prices
        decreasing_data = pd.DataFrame({
            'Close': list(range(150, 100, -1))
        })
        
        rsi = indicators.calculate_rsi(decreasing_data, window=14)
        valid_rsi = rsi.dropna()
        # RSI should approach 0 for consistently decreasing prices
        assert valid_rsi.iloc[-1] < 20
    
    def test_macd_calculation(self, indicators, sample_stock_data):
        """Test MACD calculation"""
        macd_line, signal_line, histogram = indicators.calculate_macd(
            sample_stock_data, 
            fast=12, 
            slow=26, 
            signal=9
        )
        
        assert isinstance(macd_line, pd.Series)
        assert isinstance(signal_line, pd.Series)
        assert isinstance(histogram, pd.Series)
        
        assert len(macd_line) == len(sample_stock_data)
        assert len(signal_line) == len(sample_stock_data)
        assert len(histogram) == len(sample_stock_data)
        
        # Check that histogram = macd_line - signal_line
        valid_indices = ~(pd.isna(macd_line) | pd.isna(signal_line))
        np.testing.assert_array_almost_equal(
            histogram[valid_indices],
            macd_line[valid_indices] - signal_line[valid_indices],
            decimal=10
        )
    
    def test_macd_signal_generation(self, indicators, sample_stock_data):
        """Test MACD signal generation"""
        signals = indicators.generate_macd_signals(sample_stock_data)
        
        assert isinstance(signals, pd.Series)
        assert len(signals) == len(sample_stock_data)
        
        # Signals should be -1, 0, or 1
        valid_signals = signals.dropna()
        assert valid_signals.isin([-1, 0, 1]).all()
    
    def test_bollinger_bands(self, indicators, sample_stock_data):
        """Test Bollinger Bands calculation"""
        upper_band, middle_band, lower_band = indicators.calculate_bollinger_bands(
            sample_stock_data, 
            window=20, 
            std_dev=2
        )
        
        assert isinstance(upper_band, pd.Series)
        assert isinstance(middle_band, pd.Series)
        assert isinstance(lower_band, pd.Series)
        
        # Middle band should equal SMA
        sma_20 = indicators.calculate_sma(sample_stock_data, window=20)
        np.testing.assert_array_equal(middle_band.values, sma_20.values)
        
        # Upper band should be above middle band
        valid_indices = ~pd.isna(upper_band)
        assert (upper_band[valid_indices] >= middle_band[valid_indices]).all()
        
        # Lower band should be below middle band
        assert (lower_band[valid_indices] <= middle_band[valid_indices]).all()
    
    def test_invalid_parameters(self, indicators, sample_stock_data):
        """Test handling of invalid parameters"""
        # Test negative window
        with pytest.raises(ValueError):
            indicators.calculate_sma(sample_stock_data, window=-5)
        
        # Test zero window
        with pytest.raises(ValueError):
            indicators.calculate_sma(sample_stock_data, window=0)
        
        # Test invalid MACD parameters
        with pytest.raises(ValueError):
            indicators.calculate_macd(sample_stock_data, fast=26, slow=12)  # fast > slow
    
    def test_empty_data_handling(self, indicators):
        """Test handling of empty data"""
        empty_data = pd.DataFrame()
        
        with pytest.raises(ValueError):
            indicators.calculate_sma(empty_data, window=20)
    
    def test_missing_close_column(self, indicators):
        """Test handling of data without Close column"""
        invalid_data = pd.DataFrame({
            'Open': [100, 101, 102],
            'High': [101, 102, 103]
        })
        
        with pytest.raises(KeyError):
            indicators.calculate_sma(invalid_data, window=2)
    
    def test_volume_indicators(self, indicators, sample_stock_data):
        """Test volume-based indicators"""
        volume_sma = indicators.calculate_volume_sma(sample_stock_data, window=20)
        
        assert isinstance(volume_sma, pd.Series)
        assert len(volume_sma) == len(sample_stock_data)
        
        # Check relative volume
        relative_volume = indicators.calculate_relative_volume(sample_stock_data, window=20)
        assert isinstance(relative_volume, pd.Series)
        assert (relative_volume.dropna() > 0).all()  # Should be positive

class TestIndicatorIntegration:
    
    def test_multiple_indicators_together(self, sample_stock_data):
        """Test calculating multiple indicators on the same data"""
        indicators = TechnicalIndicators()
        
        sma_20 = indicators.calculate_sma(sample_stock_data, window=20)
        rsi = indicators.calculate_rsi(sample_stock_data, window=14)
        macd_line, signal_line, histogram = indicators.calculate_macd(sample_stock_data)
        
        # All indicators should have the same length as input data
        assert len(sma_20) == len(sample_stock_data)
        assert len(rsi) == len(sample_stock_data)
        assert len(macd_line) == len(sample_stock_data)
    
    def test_indicator_summary(self, sample_stock_data):
        """Test getting a summary of all indicators"""
        indicators = TechnicalIndicators()
        summary = indicators.get_indicator_summary(sample_stock_data)
        
        assert isinstance(summary, dict)
        expected_keys = ['sma_20', 'sma_50', 'rsi', 'macd_signal', 'current_price']
        for key in expected_keys:
            assert key in summary


class TestSupportResistanceLevels:
    """Test support and resistance level detection functionality"""
    
    @pytest.fixture
    def indicators(self):
        return TechnicalIndicators()
    
    def test_detect_support_resistance_basic_functionality(self, indicators, sample_stock_data):
        """Test basic support/resistance level detection"""
        levels = indicators.detect_support_resistance(sample_stock_data, window=20)
        
        assert isinstance(levels, dict)
        assert 'support_levels' in levels
        assert 'resistance_levels' in levels
        assert isinstance(levels['support_levels'], list)
        assert isinstance(levels['resistance_levels'], list)
    
    def test_detect_support_resistance_with_strong_levels(self, indicators):
        """Test detection with clear support/resistance patterns"""
        # Create mock data with obvious support at 100 and resistance at 200
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        # Price oscillates between 100-200 with clear levels
        close_prices = [100 + 50 * np.sin(i/10) + 50 for i in range(100)]
        test_data = pd.DataFrame({
            'Close': close_prices,
            'High': [p + 5 for p in close_prices],
            'Low': [p - 5 for p in close_prices]
        }, index=dates)
        
        levels = indicators.detect_support_resistance(test_data, window=10)
        
        assert len(levels['support_levels']) >= 1
        assert len(levels['resistance_levels']) >= 1
        # Should detect levels around 100 and 200
        assert any(90 <= level <= 110 for level in levels['support_levels'])
        assert any(190 <= level <= 210 for level in levels['resistance_levels'])
    
    def test_detect_support_resistance_strength_scores(self, indicators, sample_stock_data):
        """Test that support/resistance levels include strength scores"""
        levels = indicators.detect_support_resistance(sample_stock_data, 
                                                    window=20, 
                                                    include_strength=True)
        
        assert 'support_strength' in levels
        assert 'resistance_strength' in levels
        
        if levels['support_levels']:
            assert len(levels['support_levels']) == len(levels['support_strength'])
            # Strength scores should be between 0 and 1
            assert all(0 <= strength <= 1 for strength in levels['support_strength'])
    
    def test_detect_support_resistance_invalid_input(self, indicators):
        """Test error handling with invalid input"""
        empty_data = pd.DataFrame()
        
        with pytest.raises(ValueError, match="Input data cannot be empty"):
            indicators.detect_support_resistance(empty_data)
    
    def test_detect_support_resistance_insufficient_data(self, indicators):
        """Test behavior with insufficient data"""
        # Create minimal dataset
        small_data = pd.DataFrame({
            'Close': [100, 101, 99],
            'High': [102, 103, 101], 
            'Low': [98, 99, 97]
        })
        
        levels = indicators.detect_support_resistance(small_data, window=10)
        # Should return empty lists or handle gracefully
        assert isinstance(levels['support_levels'], list)
        assert isinstance(levels['resistance_levels'], list)
    
    # Bollinger Band Strategy Tests - TDD for Micro-Phase 1C
    
    def test_calculate_bollinger_percent_b_basic(self, indicators, sample_stock_data):
        """Test %B indicator calculation - price position within Bollinger Bands"""
        # %B = (Price - Lower Band) / (Upper Band - Lower Band)
        # Values: 0 = at lower band, 0.5 = at middle (SMA), 1 = at upper band
        
        percent_b = indicators.calculate_bollinger_percent_b(sample_stock_data)
        
        # Should return Series with valid %B values
        assert isinstance(percent_b, pd.Series)
        assert len(percent_b) == len(sample_stock_data)
        
        # %B values should generally be between -0.5 and 1.5 (can exceed 0-1 range)
        # Most values should be between 0 and 1 for normal price action
        valid_range = (percent_b >= -0.5) & (percent_b <= 1.5)
        assert valid_range.sum() / len(percent_b) > 0.8  # 80% should be in reasonable range
        
        # Test specific scenario: price exactly at middle band should give %B ≈ 0.5
        upper_band, middle_band, lower_band = indicators.calculate_bollinger_bands(sample_stock_data)
        if not pd.isna(middle_band.iloc[-1]):
            # When price equals middle band, %B should be close to 0.5
            # This will fail until implementation is complete
            pass  # Will implement specific assertion after method creation
    
    def test_calculate_bollinger_percent_b_edge_cases(self, indicators):
        """Test %B calculation with edge cases"""
        # Test with insufficient data
        small_data = pd.DataFrame({
            'Close': [100, 101, 102],
            'High': [102, 103, 104],
            'Low': [98, 99, 100]
        })
        
        percent_b = indicators.calculate_bollinger_percent_b(small_data)
        
        # Should handle gracefully, early values should be NaN
        assert isinstance(percent_b, pd.Series)
        assert len(percent_b) == len(small_data)
        
        # First few values should be NaN due to insufficient data for BB calculation
        assert pd.isna(percent_b.iloc[0])  # First value should be NaN
    
    def test_detect_bollinger_squeeze_basic(self, indicators, sample_stock_data):
        """Test Bollinger Band squeeze detection for low volatility periods"""
        # Bollinger squeeze occurs when bands contract (low volatility)
        # Indicates potential breakout opportunity
        
        squeeze_info = indicators.detect_bollinger_squeeze(sample_stock_data)
        
        # Should return dictionary with squeeze information
        assert isinstance(squeeze_info, dict)
        assert 'is_squeeze' in squeeze_info
        assert 'squeeze_intensity' in squeeze_info
        assert 'squeeze_periods' in squeeze_info
        
        # Basic validation
        assert isinstance(squeeze_info['is_squeeze'], bool)
        assert isinstance(squeeze_info['squeeze_intensity'], (int, float)) or pd.isna(squeeze_info['squeeze_intensity'])
        assert isinstance(squeeze_info['squeeze_periods'], int)
        assert squeeze_info['squeeze_periods'] >= 0
        
        # Squeeze intensity should be a reasonable value (0.0 to 1.0 scale)
        if not pd.isna(squeeze_info['squeeze_intensity']):
            assert 0.0 <= squeeze_info['squeeze_intensity'] <= 1.0
    
    def test_detect_bollinger_squeeze_historical_periods(self, indicators):
        """Test squeeze detection with controlled data scenarios"""
        # Create data with known low volatility period (squeeze)
        dates = pd.date_range('2023-01-01', periods=50, freq='D')
        
        # Low volatility period: prices stay very close to each other
        low_vol_prices = [100.0] * 20 + [100.1] * 15 + [99.9] * 15
        high_vol_data = pd.DataFrame({
            'Close': low_vol_prices,
            'High': [p + 0.5 for p in low_vol_prices],
            'Low': [p - 0.5 for p in low_vol_prices]
        }, index=dates)
        
        squeeze_low_vol = indicators.detect_bollinger_squeeze(high_vol_data, squeeze_threshold=0.05)
        
        # Low volatility should trigger squeeze detection
        # This test will fail until implementation is complete
        assert isinstance(squeeze_low_vol, dict)
        # After implementation: assert squeeze_low_vol['is_squeeze'] == True
        
        # Create data with high volatility (no squeeze)  
        high_vol_prices = list(range(80, 120, 1)) + list(range(120, 80, -1))  # Trending data
        high_vol_data = pd.DataFrame({
            'Close': high_vol_prices,
            'High': [p + 2 for p in high_vol_prices],
            'Low': [p - 2 for p in high_vol_prices]
        }, index=pd.date_range('2023-01-01', periods=len(high_vol_prices), freq='D'))
        
        squeeze_high_vol = indicators.detect_bollinger_squeeze(high_vol_data, squeeze_threshold=0.05)
        
        # High volatility should NOT trigger squeeze 
        assert isinstance(squeeze_high_vol, dict)
        # After implementation: assert squeeze_high_vol['is_squeeze'] == False
    
    def test_bollinger_band_breakout_signals(self, indicators, sample_stock_data):
        """Test detection of Bollinger Band breakout signals"""
        # Test price breaking above/below bands for breakout confirmation
        
        breakout_signals = indicators.detect_bollinger_breakouts(sample_stock_data)
        
        # Should return dictionary with breakout information
        assert isinstance(breakout_signals, dict)
        assert 'upper_breakout' in breakout_signals
        assert 'lower_breakout' in breakout_signals  
        assert 'breakout_strength' in breakout_signals
        
        # Breakout flags should be boolean
        assert isinstance(breakout_signals['upper_breakout'], bool)
        assert isinstance(breakout_signals['lower_breakout'], bool)
        
        # Breakout strength should be numeric (0.0 to 1.0) or NaN
        strength = breakout_signals['breakout_strength']
        assert isinstance(strength, (int, float)) or pd.isna(strength)
        
        if not pd.isna(strength):
            assert 0.0 <= strength <= 1.0
    
    def test_bb_status_field_functionality(self, indicators):
        """Test bb_status field reports correct status in different scenarios"""
        # Test 1: Insufficient data scenario
        small_data = pd.DataFrame({
            'Close': [100, 101, 102, 103, 104, 105],
            'Volume': [1000, 1100, 1200, 1300, 1400, 1500]
        })
        
        result = indicators.get_indicator_summary(small_data)
        bb_status = result.get('bb_status')
        
        assert bb_status is not None
        assert 'insufficient_data' in bb_status
        assert '6/20' in bb_status  # Should show actual data count
        
        # BB indicators should have safe default values
        assert result.get('bollinger_percent_b') is None
        squeeze_info = result.get('bollinger_squeeze', {})
        assert squeeze_info.get('is_squeeze') == False
        breakout_info = result.get('bollinger_breakout', {})
        assert breakout_info.get('upper_breakout') == False
    
    def test_bb_status_with_sufficient_data(self, indicators):
        """Test bb_status reports 'available' with sufficient data"""
        # Create sufficient data (25 days)
        dates = pd.date_range('2023-01-01', periods=25, freq='D')
        sufficient_data = pd.DataFrame({
            'Close': [100 + i*0.5 for i in range(25)],
            'Volume': [1000 + i*10 for i in range(25)]
        }, index=dates)
        
        result = indicators.get_indicator_summary(sufficient_data)
        bb_status = result.get('bb_status')
        
        assert bb_status == 'available'
        
        # BB indicators should be calculated
        assert result.get('bollinger_percent_b') is not None
        assert isinstance(result.get('bollinger_squeeze'), dict)
        assert isinstance(result.get('bollinger_breakout'), dict)
    
    def test_bb_status_error_reporting(self, indicators):
        """Test bb_status reports calculation errors appropriately"""
        # Test with edge case that might cause calculation errors
        # (very small price changes that might cause division issues)
        flat_data = pd.DataFrame({
            'Close': [100.0] * 25,  # Completely flat prices
            'Volume': [1000] * 25
        })
        
        result = indicators.get_indicator_summary(flat_data)
        bb_status = result.get('bb_status')
        
        # Should either be 'available' or report a specific calculation error
        assert bb_status is not None
        assert isinstance(bb_status, str)
        
        # Should have safe fallback values regardless of calculation success
        squeeze_info = result.get('bollinger_squeeze', {})
        breakout_info = result.get('bollinger_breakout', {})
        assert isinstance(squeeze_info, dict)
        assert isinstance(breakout_info, dict)