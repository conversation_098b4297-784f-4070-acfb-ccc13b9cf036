import pytest
import json
import io
from datetime import datetime
from src.cli.formatters import <PERSON><PERSON><PERSON><PERSON><PERSON>, J<PERSON><PERSON><PERSON><PERSON><PERSON>, CSVFormatter, format_currency, format_percentage

class TestTerminalFormatter:
    
    @pytest.fixture
    def formatter(self):
        return TerminalFormatter()
    
    def test_basic_terminal_formatting(self, formatter, sample_analysis_result):
        """Test basic terminal output formatting"""
        output = formatter.format_analysis(sample_analysis_result)
        
        assert isinstance(output, str)
        assert 'RELIANCE.BO' in output
        assert 'BUY' in output
        assert '78%' in output
        assert '₹2,654.50' in output
        
        # Check for section headers
        assert 'RECOMMENDATION' in output
        assert 'KEY METRICS' in output
    
    def test_terminal_box_formatting(self, formatter, sample_analysis_result):
        """Test terminal box drawing characters"""
        output = formatter.format_analysis(sample_analysis_result)
        
        # Should contain box drawing characters
        assert '┌' in output or '┐' in output or '└' in output or '┘' in output
        assert '─' in output  # Horizontal line
        assert '│' in output  # Vertical line
    
    def test_recommendation_colors(self, formatter):
        """Test color coding for different recommendations"""
        buy_result = {'recommendation': 'BUY', 'confidence': 0.8, 'symbol': 'TEST.BO'}
        sell_result = {'recommendation': 'SELL', 'confidence': 0.7, 'symbol': 'TEST.BO'}
        hold_result = {'recommendation': 'HOLD', 'confidence': 0.5, 'symbol': 'TEST.BO'}
        
        buy_output = formatter.format_analysis(buy_result)
        sell_output = formatter.format_analysis(sell_result)
        hold_output = formatter.format_analysis(hold_result)
        
        # Should contain recommendation text
        assert 'BUY' in buy_output
        assert 'SELL' in sell_output
        assert 'HOLD' in hold_output
    
    def test_detailed_vs_basic_formatting(self, formatter, sample_analysis_result):
        """Test difference between detailed and basic formatting"""
        basic_output = formatter.format_analysis(sample_analysis_result, detailed=False)
        detailed_output = formatter.format_analysis(sample_analysis_result, detailed=True)
        
        # Detailed should be longer
        assert len(detailed_output) > len(basic_output)
        
        # Detailed should contain more technical indicators
        assert 'Bollinger Upper' in detailed_output or 'Bollinger Bands' in detailed_output
        assert 'Relative Volume' in detailed_output or 'Volume Analysis' in detailed_output
    
    def test_missing_data_handling(self, formatter):
        """Test handling of missing data fields"""
        incomplete_result = {
            'symbol': 'TEST.BO',
            'recommendation': 'BUY',
            'current_price': 100.0
            # Missing many fields
        }
        
        output = formatter.format_analysis(incomplete_result)
        
        assert 'TEST.BO' in output
        assert 'BUY' in output
        assert '₹100.00' in output
        assert 'N/A' in output or '--' in output  # Missing data indicators
    
    def test_error_message_formatting(self, formatter):
        """Test error message formatting"""
        error_msg = "Network connection failed"
        output = formatter.format_error(error_msg)
        
        assert 'Error' in output
        assert error_msg in output
        assert len(output.strip()) > 0

class TestJSONFormatter:
    
    @pytest.fixture
    def formatter(self):
        return JSONFormatter()
    
    def test_json_formatting(self, formatter, sample_analysis_result):
        """Test JSON output formatting"""
        output = formatter.format_analysis(sample_analysis_result)
        
        # Should be valid JSON
        parsed = json.loads(output)
        assert isinstance(parsed, dict)
        
        # Check key fields
        assert parsed['symbol'] == 'RELIANCE.BO'
        assert parsed['recommendation']['action'] == 'BUY'
        assert parsed['recommendation']['confidence'] == 0.78
        assert parsed['current_price'] == 2654.50
    
    def test_json_structure(self, formatter, sample_analysis_result):
        """Test JSON structure and nesting"""
        output = formatter.format_analysis(sample_analysis_result)
        parsed = json.loads(output)
        
        # Check nested structures
        assert 'recommendation' in parsed
        assert 'technical_indicators' in parsed
        assert 'signals' in parsed
        
        # Check recommendation structure
        rec = parsed['recommendation']
        assert 'action' in rec
        assert 'confidence' in rec
        assert 'target_price' in rec
        assert 'risk_level' in rec
    
    def test_json_timestamp_inclusion(self, formatter, sample_analysis_result):
        """Test that timestamp is included in JSON"""
        output = formatter.format_analysis(sample_analysis_result)
        parsed = json.loads(output)
        
        assert 'timestamp' in parsed
        # Should be valid ISO format
        datetime.fromisoformat(parsed['timestamp'].replace('Z', '+00:00'))
    
    def test_json_pretty_printing(self, formatter, sample_analysis_result):
        """Test JSON pretty printing option"""
        output = formatter.format_analysis(sample_analysis_result, pretty=True)
        
        # Pretty printed JSON should have newlines and indentation
        assert '\n' in output
        assert '  ' in output  # Indentation
        
        # Should still be valid JSON
        parsed = json.loads(output)
        assert parsed['symbol'] == 'RELIANCE.BO'
    
    def test_json_null_handling(self, formatter):
        """Test JSON handling of None/null values"""
        result_with_nulls = {
            'symbol': 'TEST.BO',
            'current_price': 100.0,
            'sma_20': None,
            'recommendation': 'HOLD',
            'confidence': 0.5
        }
        
        output = formatter.format_analysis(result_with_nulls)
        parsed = json.loads(output)
        
        # Should preserve null values as null in JSON
        assert parsed['technical_indicators']['sma_20'] is None
        assert parsed['symbol'] == 'TEST.BO'

class TestCSVFormatter:
    
    @pytest.fixture  
    def formatter(self):
        return CSVFormatter()
    
    def test_csv_formatting(self, formatter, sample_analysis_result):
        """Test CSV output formatting"""
        output = formatter.format_analysis(sample_analysis_result)
        
        lines = output.strip().split('\n')
        assert len(lines) >= 2  # Header + data row
        
        # Check header
        header = lines[0]
        assert 'symbol' in header
        assert 'current_price' in header
        assert 'recommendation' in header
        
        # Check data row
        data_row = lines[1]
        assert 'RELIANCE.BO' in data_row
        assert 'BUY' in data_row
        assert '2654.5' in data_row
    
    def test_csv_header_consistency(self, formatter, sample_analysis_result):
        """Test CSV header matches data columns"""
        output = formatter.format_analysis(sample_analysis_result)
        lines = output.strip().split('\n')
        
        header_cols = lines[0].split(',')
        data_cols = lines[1].split(',')
        
        assert len(header_cols) == len(data_cols)
    
    def test_csv_special_character_escaping(self, formatter):
        """Test CSV escaping of special characters"""
        result_with_specials = {
            'symbol': 'TEST,INC.BO',  # Symbol with comma that should be handled
            'recommendation': 'BUY "STRONG"',  # Recommendation with quotes
            'current_price': 100.0
        }
        
        output = formatter.format_analysis(result_with_specials)
        
        # Should properly escape quotes and commas in symbol or recommendation
        assert 'TEST,INC.BO' in output or '"TEST,INC.BO"' in output
    
    def test_csv_numeric_formatting(self, formatter, sample_analysis_result):
        """Test CSV numeric value formatting"""
        output = formatter.format_analysis(sample_analysis_result)
        
        # Should contain numeric values as strings
        assert '2654.5' in output  # current_price
        assert '0.78' in output    # confidence
        assert '42.5' in output    # rsi
    
    def test_csv_batch_formatting(self, formatter):
        """Test CSV formatting for multiple stocks"""
        results = [
            {'symbol': 'RELIANCE.BO', 'recommendation': 'BUY', 'current_price': 2650.0},
            {'symbol': 'TCS.BO', 'recommendation': 'HOLD', 'current_price': 3200.0},
            {'symbol': 'INFY.BO', 'recommendation': 'SELL', 'current_price': 1400.0}
        ]
        
        output = formatter.format_batch(results)
        lines = output.strip().split('\n')
        
        assert len(lines) == 4  # Header + 3 data rows
        assert 'RELIANCE.BO' in lines[1]
        assert 'TCS.BO' in lines[2]
        assert 'INFY.BO' in lines[3]

class TestUtilityFunctions:
    
    def test_format_currency(self):
        """Test currency formatting function"""
        assert format_currency(2654.50) == '₹2,654.50'
        assert format_currency(1000000) == '₹10,00,000.00'  # Indian numbering
        assert format_currency(0) == '₹0.00'
        assert format_currency(-100.5) == '-₹100.50'
    
    def test_format_percentage(self):
        """Test percentage formatting function"""
        assert format_percentage(0.78) == '78.0%'
        assert format_percentage(0.125) == '12.5%'
        assert format_percentage(1.0) == '100.0%'
        assert format_percentage(-0.05) == '-5.0%'
        assert format_percentage(0.0) == '0.0%'
    
    def test_format_percentage_with_precision(self):
        """Test percentage formatting with different precision"""
        assert format_percentage(0.12345, precision=2) == '12.35%'
        assert format_percentage(0.12345, precision=0) == '12%'
        assert format_percentage(0.12345, precision=4) == '12.3450%'

class TestFormatterIntegration:
    
    def test_formatter_factory(self):
        """Test formatter factory function"""
        from src.cli.formatters import get_formatter
        
        terminal_formatter = get_formatter('table')
        json_formatter = get_formatter('json')
        csv_formatter = get_formatter('csv')
        
        assert isinstance(terminal_formatter, TerminalFormatter)
        assert isinstance(json_formatter, JSONFormatter)
        assert isinstance(csv_formatter, CSVFormatter)
    
    def test_invalid_formatter_type(self):
        """Test handling of invalid formatter type"""
        from src.cli.formatters import get_formatter
        
        with pytest.raises(ValueError) as exc_info:
            get_formatter('invalid')
        
        assert 'Unsupported format' in str(exc_info.value)
    
    def test_formatter_consistency(self, sample_analysis_result):
        """Test that all formatters handle the same data consistently"""
        from src.cli.formatters import get_formatter
        
        formatters = {
            'table': get_formatter('table'),
            'json': get_formatter('json'),
            'csv': get_formatter('csv')
        }
        
        outputs = {}
        for name, formatter in formatters.items():
            outputs[name] = formatter.format_analysis(sample_analysis_result)
            
            # All should contain the symbol
            assert 'RELIANCE.BO' in outputs[name]
            assert len(outputs[name]) > 0
    
    def test_error_handling_across_formatters(self):
        """Test error handling consistency across formatters"""
        from src.cli.formatters import get_formatter
        
        error_msg = "Test error message"
        formatters = ['table', 'json', 'csv']
        
        for format_type in formatters:
            formatter = get_formatter(format_type)
            error_output = formatter.format_error(error_msg)
            
            assert error_msg in error_output
            assert len(error_output) > 0


class TestBollingerBandFormatterIntegration:
    """Test Bollinger Band integration in formatters"""
    
    def test_terminal_formatter_bb_display_with_sufficient_data(self):
        """Test terminal formatter displays BB indicators with sufficient data"""
        from src.cli.formatters import TerminalFormatter
        
        # Mock result with BB indicators available
        mock_result = {
            'symbol': 'TEST.BO',
            'current_price': 1500.0,
            'recommendation': {'action': 'BUY', 'confidence': 0.7, 'target_price': 1600, 'stop_loss': 1400},
            'sma_20': 1450.0,
            'rsi': 45.0,
            'bb_status': 'available',
            'bollinger_percent_b': 0.65,  # Moderate position
            'bollinger_squeeze': {'is_squeeze': False, 'squeeze_intensity': 0.3, 'squeeze_periods': 2},
            'bollinger_breakout': {'upper_breakout': False, 'lower_breakout': False, 'breakout_strength': 0.1}
        }
        
        formatter = TerminalFormatter()
        output = formatter.format_analysis(mock_result, detailed=False)
        
        # Should display %B position
        assert '%B Position: 0.65' in output
        # Should not show overbought/oversold labels for moderate values
        assert 'Overbought' not in output
        assert 'Oversold' not in output
        # Should not show squeeze since is_squeeze is False
        assert 'BB Squeeze:' not in output
    
    def test_terminal_formatter_bb_overbought_oversold_display(self):
        """Test terminal formatter shows overbought/oversold labels"""
        from src.cli.formatters import TerminalFormatter
        
        # Test overbought scenario
        overbought_result = {
            'symbol': 'TEST.BO',
            'current_price': 1500.0,
            'recommendation': {'action': 'SELL', 'confidence': 0.5, 'target_price': 1400, 'stop_loss': 1600},
            'sma_20': 1450.0,
            'rsi': 75.0,
            'bb_status': 'available',
            'bollinger_percent_b': 0.85,  # Overbought
            'bollinger_squeeze': {'is_squeeze': False, 'squeeze_intensity': 0.2, 'squeeze_periods': 1},
            'bollinger_breakout': {'upper_breakout': False, 'lower_breakout': False, 'breakout_strength': 0.0}
        }
        
        formatter = TerminalFormatter()
        output = formatter.format_analysis(overbought_result, detailed=False)
        
        assert '%B Position: 0.85 (Overbought)' in output
        
        # Test oversold scenario
        oversold_result = overbought_result.copy()
        oversold_result['bollinger_percent_b'] = 0.15  # Oversold
        oversold_result['recommendation']['action'] = 'BUY'
        
        output = formatter.format_analysis(oversold_result, detailed=False)
        assert '%B Position: 0.15 (Oversold)' in output
    
    def test_terminal_formatter_bb_squeeze_display(self):
        """Test terminal formatter displays Bollinger squeeze status"""
        from src.cli.formatters import TerminalFormatter
        
        squeeze_result = {
            'symbol': 'TEST.BO',
            'current_price': 1500.0,
            'recommendation': {'action': 'HOLD', 'confidence': 0.6, 'target_price': 1550, 'stop_loss': 1450},
            'sma_20': 1500.0,
            'rsi': 50.0,
            'bb_status': 'available',
            'bollinger_percent_b': 0.5,
            'bollinger_squeeze': {'is_squeeze': True, 'squeeze_intensity': 0.7, 'squeeze_periods': 8},
            'bollinger_breakout': {'upper_breakout': False, 'lower_breakout': False, 'breakout_strength': 0.0}
        }
        
        formatter = TerminalFormatter()
        output = formatter.format_analysis(squeeze_result, detailed=False)
        
        # Should show squeeze with intensity
        assert 'BB Squeeze: Active (0.7)' in output
    
    def test_terminal_formatter_bb_insufficient_data_display(self):
        """Test terminal formatter displays BB status message for insufficient data"""
        from src.cli.formatters import TerminalFormatter
        
        insufficient_result = {
            'symbol': 'TEST.BO',
            'current_price': 105.0,
            'recommendation': {'action': 'HOLD', 'confidence': 0.3, 'target_price': 110, 'stop_loss': 100},
            'sma_20': None,
            'rsi': None,
            'bb_status': 'insufficient_data: 6/20 periods',
            'bollinger_percent_b': None,
            'bollinger_squeeze': {'is_squeeze': False, 'squeeze_intensity': None, 'squeeze_periods': 0},
            'bollinger_breakout': {'upper_breakout': False, 'lower_breakout': False, 'breakout_strength': None}
        }
        
        formatter = TerminalFormatter()
        output = formatter.format_analysis(insufficient_result, detailed=False)
        
        # Should display the status message
        assert 'BB Analysis: insufficient_data: 6/20 periods' in output
        # Should not show %B position since it's None
        assert '%B Position:' not in output
    
    def test_terminal_formatter_bb_detailed_mode(self):
        """Test terminal formatter shows breakout information in detailed mode"""
        from src.cli.formatters import TerminalFormatter
        
        breakout_result = {
            'symbol': 'TEST.BO',
            'current_price': 1600.0,
            'recommendation': {'action': 'BUY', 'confidence': 0.8, 'target_price': 1700, 'stop_loss': 1500},
            'sma_20': 1550.0,
            'rsi': 60.0,
            'bb_status': 'available',
            'bb_upper': 1650.0,
            'bollinger_percent_b': 0.95,
            'bollinger_squeeze': {'is_squeeze': False, 'squeeze_intensity': 0.2, 'squeeze_periods': 1},
            'bollinger_breakout': {'upper_breakout': True, 'lower_breakout': False, 'breakout_strength': 0.6},
            'relative_volume': 1.8
        }
        
        formatter = TerminalFormatter()
        output = formatter.format_analysis(breakout_result, detailed=True)
        
        # Should show breakout information in detailed mode
        assert 'BB Breakout: Upper (0.60)' in output
        # Should show other detailed info
        assert 'Bollinger Upper:' in output
        assert 'Relative Volume: 1.8' in output