import pytest
from unittest.mock import Mock, patch
import pandas as pd
from datetime import datetime, timedelta
from src.data.yahoo_client import Yahoo<PERSON>lient, StockData

class TestYahooClient:
    
    @pytest.fixture
    def client(self):
        return YahooClient()
    
    def test_client_initialization(self, client):
        """Test that Yahoo<PERSON>lient initializes properly"""
        assert client is not None
        assert hasattr(client, 'fetch_stock_data')
    
    def test_valid_bse_symbol_format(self, client, bse_symbols):
        """Test validation of BSE stock symbols"""
        for symbol in bse_symbols:
            assert client.is_valid_bse_symbol(symbol)
    
    def test_invalid_symbol_format(self, client):
        """Test rejection of invalid symbol formats"""
        invalid_symbols = [
            "RELIANCE",  # Missing .BO
            "TCS.NS",    # Wrong exchange
            "",          # Empty string
            None,        # None value
            "123.BO"     # Invalid format
        ]
        
        for symbol in invalid_symbols:
            assert not client.is_valid_bse_symbol(symbol)
    
    @patch('yfinance.Ticker')
    def test_fetch_stock_data_success(self, mock_ticker, client, sample_stock_data):
        """Test successful stock data fetching"""
        mock_ticker_instance = Mock()
        mock_ticker_instance.history.return_value = sample_stock_data
        mock_ticker.return_value = mock_ticker_instance
        
        result = client.fetch_stock_data("RELIANCE.BO", period="1y")
        
        assert isinstance(result, StockData)
        assert result.symbol == "RELIANCE.BO"
        assert result.data is not None
        assert not result.data.empty
        mock_ticker.assert_called_once_with("RELIANCE.BO")
    
    @patch('yfinance.Ticker')
    def test_fetch_stock_data_network_error(self, mock_ticker, client):
        """Test handling of network errors"""
        mock_ticker_instance = Mock()
        mock_ticker_instance.history.side_effect = Exception("Network error")
        mock_ticker.return_value = mock_ticker_instance
        
        with pytest.raises(Exception) as exc_info:
            client.fetch_stock_data("RELIANCE.BO")
        
        assert "Network error" in str(exc_info.value)
    
    @patch('yfinance.Ticker')
    def test_fetch_stock_data_empty_response(self, mock_ticker, client):
        """Test handling of empty data response"""
        mock_ticker_instance = Mock()
        mock_ticker_instance.history.return_value = pd.DataFrame()
        mock_ticker.return_value = mock_ticker_instance
        
        with pytest.raises(ValueError) as exc_info:
            client.fetch_stock_data("INVALID.BO")
        
        assert "No data found" in str(exc_info.value)
    
    def test_period_validation(self, client):
        """Test validation of period parameter"""
        valid_periods = ["1m", "3m", "6m", "1y", "2y"]
        invalid_periods = ["1w", "5y", "invalid", None]
        
        for period in valid_periods:
            assert client.is_valid_period(period)
        
        for period in invalid_periods:
            assert not client.is_valid_period(period)

class TestStockData:
    
    def test_stock_data_creation(self, sample_stock_data):
        """Test StockData model creation"""
        stock_data = StockData(
            symbol="RELIANCE.BO",
            data=sample_stock_data,
            period="1y"
        )
        
        assert stock_data.symbol == "RELIANCE.BO"
        assert stock_data.period == "1y"
        assert isinstance(stock_data.data, pd.DataFrame)
    
    def test_current_price_property(self, sample_stock_data):
        """Test current price calculation"""
        stock_data = StockData(
            symbol="RELIANCE.BO",
            data=sample_stock_data,
            period="1y"
        )
        
        current_price = stock_data.current_price
        assert isinstance(current_price, float)
        assert current_price > 0
    
    def test_price_change_property(self, sample_stock_data):
        """Test price change calculation"""
        stock_data = StockData(
            symbol="RELIANCE.BO",
            data=sample_stock_data,
            period="1y"
        )
        
        price_change = stock_data.price_change
        assert isinstance(price_change, float)
        
        price_change_pct = stock_data.price_change_percent
        assert isinstance(price_change_pct, float)
    
    def test_data_validation(self):
        """Test data validation requirements"""
        # Test with invalid data
        with pytest.raises(ValueError):
            StockData(
                symbol="RELIANCE.BO",
                data=pd.DataFrame(),  # Empty DataFrame
                period="1y"
            )
    
    def test_required_columns(self, sample_stock_data):
        """Test that required columns are present"""
        stock_data = StockData(
            symbol="RELIANCE.BO",
            data=sample_stock_data,
            period="1y"
        )
        
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in required_columns:
            assert col in stock_data.data.columns


class TestYahooClientFundamentalData:
    """TDD tests for fundamental data fetching capabilities"""
    
    @pytest.fixture
    def client(self):
        return YahooClient()
    
    @pytest.fixture
    def mock_ticker_info(self):
        """Mock comprehensive ticker.info data"""
        return {
            # Valuation metrics
            'forwardPE': 24.5,
            'trailingPE': 22.8,
            'priceToBook': 2.1,
            'enterpriseToRevenue': 3.2,
            'enterpriseToEbitda': 12.5,
            
            # Financial metrics
            'returnOnEquity': 0.128,
            'returnOnAssets': 0.065,
            'debtToEquity': 0.35,
            'currentRatio': 1.45,
            'quickRatio': 0.95,
            
            # Growth metrics
            'revenueGrowth': 0.085,
            'earningsGrowth': 0.12,
            'quarterlyRevenueGrowth': 0.06,
            'quarterlyEarningsGrowth': 0.15,
            
            # Dividend metrics
            'dividendYield': 0.008,
            'dividendRate': 8.0,
            'payoutRatio': 0.25,
            
            # Company info
            'longName': 'Reliance Industries Limited',
            'sector': 'Energy',
            'industry': 'Oil & Gas Integrated',
            'country': 'India',
            'currency': 'INR',
            
            # Financial statement data
            'totalRevenue': ************,
            'totalDebt': ************,
            'totalCash': ************,
            'bookValue': 1250.0,
            'marketCap': 1800000000000,
            'sharesOutstanding': 6770000000,
        }
    
    @patch('yfinance.Ticker')
    def test_fetch_fundamental_data_success(self, mock_ticker, client, mock_ticker_info):
        """Test successful fundamental data fetching"""
        mock_ticker_instance = Mock()
        mock_ticker_instance.info = mock_ticker_info
        mock_ticker.return_value = mock_ticker_instance
        
        fundamental_data = client.fetch_fundamental_data("RELIANCE.BO")
        
        assert isinstance(fundamental_data, dict)
        assert 'valuation' in fundamental_data
        assert 'profitability' in fundamental_data
        assert 'growth' in fundamental_data
        assert 'dividend' in fundamental_data
        assert 'company_info' in fundamental_data
        
        # Test valuation metrics
        valuation = fundamental_data['valuation']
        assert 'pe_ratio' in valuation
        assert 'pb_ratio' in valuation
        assert valuation['pe_ratio'] == 22.8  # Should prefer trailing over forward
        assert valuation['pb_ratio'] == 2.1
        
        # Test profitability metrics
        profitability = fundamental_data['profitability']
        assert 'roe' in profitability
        assert 'roa' in profitability
        assert 'debt_to_equity' in profitability
        assert profitability['roe'] == 12.8  # Converted to percentage
        assert profitability['roa'] == 6.5   # Converted to percentage
        
        # Test growth metrics
        growth = fundamental_data['growth']
        assert 'revenue_growth_yoy' in growth
        assert 'earnings_growth_yoy' in growth
        assert growth['revenue_growth_yoy'] == 8.5  # Converted to percentage
        
        # Test dividend metrics
        dividend = fundamental_data['dividend']
        assert 'dividend_yield' in dividend
        assert dividend['dividend_yield'] == 0.8  # Converted to percentage
        
        # Test company info
        company_info = fundamental_data['company_info']
        assert 'company_name' in company_info
        assert 'sector' in company_info
        assert company_info['company_name'] == 'Reliance Industries Limited'
    
    @patch('yfinance.Ticker')
    def test_fetch_fundamental_data_with_missing_fields(self, mock_ticker, client):
        """Test fundamental data fetching with missing fields"""
        mock_ticker_instance = Mock()
        mock_ticker_instance.info = {
            'longName': 'Test Company',
            'forwardPE': 25.0,
            # Missing most other fields
        }
        mock_ticker.return_value = mock_ticker_instance
        
        fundamental_data = client.fetch_fundamental_data("TEST.BO")
        
        assert isinstance(fundamental_data, dict)
        
        # Should have structure even with missing data
        assert 'valuation' in fundamental_data
        assert 'profitability' in fundamental_data
        
        # Available data should be present
        assert fundamental_data['valuation']['pe_ratio'] == 25.0
        assert fundamental_data['company_info']['company_name'] == 'Test Company'
        
        # Missing data should be None
        assert fundamental_data['profitability']['roe'] is None
        assert fundamental_data['dividend']['dividend_yield'] is None
    
    @patch('yfinance.Ticker')
    def test_fetch_fundamental_data_ticker_error(self, mock_ticker, client):
        """Test handling of ticker fetching errors"""
        mock_ticker.side_effect = Exception("API error")
        
        with pytest.raises(Exception) as exc_info:
            client.fetch_fundamental_data("RELIANCE.BO")
        
        assert "Failed to fetch fundamental data" in str(exc_info.value)
    
    @patch('yfinance.Ticker')
    def test_fetch_fundamental_data_empty_info(self, mock_ticker, client):
        """Test handling of empty ticker.info"""
        mock_ticker_instance = Mock()
        mock_ticker_instance.info = {}
        mock_ticker.return_value = mock_ticker_instance
        
        fundamental_data = client.fetch_fundamental_data("TEST.BO")
        
        # Should return structured data with None values
        assert isinstance(fundamental_data, dict)
        assert all(section in fundamental_data for section in ['valuation', 'profitability', 'growth', 'dividend', 'company_info'])
        
        # All metrics should be None when no data available
        assert fundamental_data['valuation']['pe_ratio'] is None
        assert fundamental_data['profitability']['roe'] is None
    
    def test_fetch_fundamental_data_invalid_symbol(self, client):
        """Test fundamental data fetching with invalid symbol"""
        with pytest.raises(ValueError) as exc_info:
            client.fetch_fundamental_data("INVALID")
        
        assert "Invalid BSE symbol format" in str(exc_info.value)
    
    @patch('yfinance.Ticker')
    def test_fetch_complete_stock_info_success(self, mock_ticker, client, sample_stock_data, mock_ticker_info):
        """Test fetching complete stock information (price + fundamental data)"""
        mock_ticker_instance = Mock()
        mock_ticker_instance.history.return_value = sample_stock_data
        mock_ticker_instance.info = mock_ticker_info
        mock_ticker.return_value = mock_ticker_instance
        
        complete_data = client.fetch_complete_stock_info("RELIANCE.BO", period="1y")
        
        # Should contain both price data and fundamental data
        assert isinstance(complete_data, dict)
        assert 'stock_data' in complete_data
        assert 'fundamental_data' in complete_data
        
        # Stock data should be StockData instance
        assert isinstance(complete_data['stock_data'], StockData)
        assert complete_data['stock_data'].symbol == "RELIANCE.BO"
        
        # Fundamental data should be structured dict
        fundamental_data = complete_data['fundamental_data']
        assert 'valuation' in fundamental_data
        assert 'profitability' in fundamental_data
    
    @patch('yfinance.Ticker')
    def test_fetch_complete_stock_info_partial_failure(self, mock_ticker, client, sample_stock_data):
        """Test complete stock info fetching when fundamental data fails"""
        mock_ticker_instance = Mock()
        mock_ticker_instance.history.return_value = sample_stock_data
        # Simulate fundamental data fetch error
        mock_ticker_instance.info = None  # This should cause an error in processing
        mock_ticker.return_value = mock_ticker_instance
        
        complete_data = client.fetch_complete_stock_info("RELIANCE.BO", period="1y")
        
        # Should still return price data even if fundamental fails
        assert isinstance(complete_data, dict)
        assert 'stock_data' in complete_data
        assert isinstance(complete_data['stock_data'], StockData)
        
        # Fundamental data should be None or empty structure
        assert complete_data['fundamental_data'] is not None  # Should gracefully handle
    
    def test_cache_fundamental_data(self, client):
        """Test caching of fundamental data to avoid repeated API calls"""
        with patch('yfinance.Ticker') as mock_ticker:
            mock_ticker_instance = Mock()
            mock_ticker_instance.info = {'forwardPE': 25.0, 'longName': 'Test Company'}
            mock_ticker.return_value = mock_ticker_instance
            
            # First call
            data1 = client.fetch_fundamental_data("RELIANCE.BO")
            
            # Second call (should use cache if implemented)
            data2 = client.fetch_fundamental_data("RELIANCE.BO")
            
            # Results should be identical
            assert data1 == data2
            
            # This test defines expected caching behavior
            # Implementation may choose to cache or not
    
    @patch('yfinance.Ticker')
    def test_fundamental_data_validation(self, mock_ticker, client):
        """Test validation of fundamental data before processing"""
        mock_ticker_instance = Mock()
        # Mock invalid data that should be filtered out
        mock_ticker_instance.info = {
            'forwardPE': -5.0,     # Invalid negative P/E
            'priceToBook': 0.0,    # Invalid zero P/B
            'returnOnEquity': 5.0, # ROE > 1 (should be decimal)
            'longName': 'Valid Company Name',
        }
        mock_ticker.return_value = mock_ticker_instance
        
        fundamental_data = client.fetch_fundamental_data("TEST.BO")
        
        # Invalid values should be filtered out (set to None)
        assert fundamental_data['valuation']['pe_ratio'] is None  # Negative filtered out
        assert fundamental_data['valuation']['pb_ratio'] is None  # Zero filtered out
        
        # Valid data should be preserved
        assert fundamental_data['company_info']['company_name'] == 'Valid Company Name'
    
    def test_data_format_standardization(self, client):
        """Test that all fundamental metrics are returned in standardized format"""
        # This test defines the expected data structure
        expected_structure = {
            'valuation': ['pe_ratio', 'pb_ratio', 'enterprise_to_revenue', 'enterprise_to_ebitda'],
            'profitability': ['roe', 'roa', 'debt_to_equity', 'current_ratio', 'quick_ratio'],
            'growth': ['revenue_growth_yoy', 'revenue_growth_qoq', 'earnings_growth_yoy', 'earnings_growth_qoq'],
            'dividend': ['dividend_yield', 'dividend_rate', 'payout_ratio'],
            'company_info': ['company_name', 'sector', 'industry', 'country', 'currency', 'market_cap']
        }
        
        # This defines the expected interface - implementation will ensure this structure
        with patch('yfinance.Ticker') as mock_ticker:
            mock_ticker_instance = Mock()
            mock_ticker_instance.info = {}
            mock_ticker.return_value = mock_ticker_instance
            
            result = client.fetch_fundamental_data("TEST.BO")
            
            # Verify structure exists
            for section, metrics in expected_structure.items():
                assert section in result
                for metric in metrics:
                    assert metric in result[section]