import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch
from src.engine.enhanced_recommendation_engine import EnhancedRecommendationEngine
from src.data.yahoo_client import StockData
from src.analysis.fundamental import FundamentalAnalysis

class TestEnhancedRecommendationEngine:
    """TDD tests for Enhanced Recommendation Engine - Phase 2A"""
    
    @pytest.fixture
    def engine(self):
        """Create engine with default factor weights"""
        return EnhancedRecommendationEngine()
    
    @pytest.fixture
    def custom_engine(self):
        """Create engine with custom factor weights"""
        weights = {
            'technical': 0.4,
            'fundamental': 0.3,
            'risk': 0.2,
            'market_context': 0.1
        }
        return EnhancedRecommendationEngine(factor_weights=weights)
    
    @pytest.fixture
    def mock_stock_data(self, sample_stock_data):
        return StockData(
            symbol="RELIANCE.BO",
            data=sample_stock_data,
            period="1y"
        )
    
    # TDD Cycle 1: Enhanced Recommendation Engine Structure Tests
    
    def test_engine_initialization(self, engine):
        """Test that EnhancedRecommendationEngine initializes properly"""
        assert engine is not None
        assert hasattr(engine, 'generate_enhanced_recommendation')
        assert hasattr(engine, 'factor_weights')
        
        # Default weights should sum to 1.0
        assert abs(sum(engine.factor_weights.values()) - 1.0) < 0.001
        
        # Should have all required components
        assert hasattr(engine, 'technical_scorer')
        assert hasattr(engine, 'fundamental_scorer')
        assert hasattr(engine, 'risk_scorer')
        assert hasattr(engine, 'market_context_analyzer')
    
    def test_custom_factor_weights_initialization(self, custom_engine):
        """Test initialization with custom factor weights"""
        expected_weights = {
            'technical': 0.4,
            'fundamental': 0.3,
            'risk': 0.2,
            'market_context': 0.1
        }
        
        assert custom_engine.factor_weights == expected_weights
        assert abs(sum(custom_engine.factor_weights.values()) - 1.0) < 0.001
    
    def test_invalid_factor_weights_raises_error(self):
        """Test that invalid factor weights raise validation errors"""
        # Missing required factors
        invalid_weights_1 = {'technical': 0.7, 'fundamental': 0.3}  # Missing risk and market_context
        with pytest.raises(ValueError, match="Missing required factors"):
            EnhancedRecommendationEngine(factor_weights=invalid_weights_1)
        
        # Negative weights
        invalid_weights_2 = {'technical': 0.6, 'fundamental': -0.1, 'risk': 0.3, 'market_context': 0.2}
        with pytest.raises(ValueError, match="Factor weights must be non-negative"):
            EnhancedRecommendationEngine(factor_weights=invalid_weights_2)
        
        # Weights don't sum to 1.0
        invalid_weights_3 = {'technical': 0.5, 'fundamental': 0.3, 'risk': 0.1, 'market_context': 0.05}
        with pytest.raises(ValueError, match="Factor weights must sum to 1.0"):
            EnhancedRecommendationEngine(factor_weights=invalid_weights_3)
    
    def test_multi_factor_scoring_structure(self, engine, mock_stock_data):
        """Test multi-factor scoring returns structured score breakdown"""
        with patch.object(engine.technical_scorer, 'calculate_score') as mock_tech, \
             patch.object(engine.fundamental_scorer, 'calculate_score') as mock_fund, \
             patch.object(engine.risk_scorer, 'calculate_score') as mock_risk, \
             patch.object(engine.market_context_analyzer, 'calculate_score') as mock_market:
            
            # Mock individual scores
            mock_tech.return_value = {'score': 0.6, 'details': {'rsi': 45, 'trend': 'bullish'}}
            mock_fund.return_value = {'score': 0.4, 'details': {'pe_ratio': 22.5, 'roe': 15.2}}
            mock_risk.return_value = {'score': -0.2, 'details': {'var_95': -0.03, 'volatility': 0.25}}
            mock_market.return_value = {'score': 0.3, 'details': {'sector_performance': 'outperform'}}
            
            result = engine.calculate_multi_factor_scores(mock_stock_data)
            
            # Should return structured breakdown
            assert isinstance(result, dict)
            assert 'technical' in result
            assert 'fundamental' in result
            assert 'risk' in result
            assert 'market_context' in result
            assert 'composite_score' in result
            assert 'weighted_scores' in result
            
            # Each factor should have score and details
            for factor in ['technical', 'fundamental', 'risk', 'market_context']:
                assert 'score' in result[factor]
                assert 'details' in result[factor]
    
    def test_recommendation_includes_all_required_fields(self, engine, mock_stock_data):
        """Test enhanced recommendation includes all required fields"""
        # Mock the scoring and analysis methods
        with patch.object(engine, 'calculate_multi_factor_scores') as mock_scores, \
             patch.object(engine, 'calculate_enhanced_confidence') as mock_conf, \
             patch.object(engine, 'calculate_advanced_price_targets') as mock_targets, \
             patch.object(engine, 'generate_recommendation_rationale') as mock_rationale:
            
            mock_scores.return_value = {
                'composite_score': 0.4,
                'technical': {'score': 0.6}, 'fundamental': {'score': 0.3},
                'risk': {'score': -0.1}, 'market_context': {'score': 0.2}
            }
            mock_conf.return_value = {'confidence': 0.75, 'confidence_interval': (0.65, 0.85)}
            mock_targets.return_value = {'target_price': 2800.0, 'stop_loss': 2500.0, 'position_size': 0.02}
            mock_rationale.return_value = {'primary_factors': ['technical strength', 'moderate valuation']}
            
            recommendation = engine.generate_enhanced_recommendation(mock_stock_data)
            
            # Should include all required fields
            required_fields = [
                'action', 'confidence', 'confidence_interval', 'target_price', 'stop_loss',
                'position_size', 'rationale', 'factor_scores', 'risk_level', 'time_horizon',
                'scenario_analysis', 'key_risks', 'key_opportunities'
            ]
            
            for field in required_fields:
                assert field in recommendation, f"Missing required field: {field}"
            
            # Action should be one of valid options
            assert recommendation['action'] in ['BUY', 'SELL', 'HOLD']
            
            # Confidence should be between 0 and 1
            assert 0 <= recommendation['confidence'] <= 1
            
            # Price targets should be positive
            assert recommendation['target_price'] > 0
            assert recommendation['stop_loss'] > 0
    
    def test_composite_score_calculation(self, engine, mock_stock_data):
        """Test composite score calculation using factor weights"""
        # Mock individual factor scores
        factor_scores = {
            'technical': {'score': 0.8, 'details': {}},
            'fundamental': {'score': 0.4, 'details': {}},
            'risk': {'score': -0.2, 'details': {}},
            'market_context': {'score': 0.6, 'details': {}}
        }
        
        composite_score = engine._calculate_composite_score(factor_scores)
        
        # Calculate expected weighted score
        weights = engine.factor_weights
        expected_score = (
            0.8 * weights['technical'] +
            0.4 * weights['fundamental'] +
            (-0.2) * weights['risk'] +
            0.6 * weights['market_context']
        )
        
        assert abs(composite_score - expected_score) < 0.001
    
    def test_action_determination_logic(self, engine):
        """Test how composite scores translate to buy/sell/hold actions"""
        # Strong positive score should be BUY
        strong_buy_score = 0.7
        action_1 = engine._determine_action(strong_buy_score, confidence=0.8)
        assert action_1 == 'BUY'
        
        # Strong negative score should be SELL
        strong_sell_score = -0.7
        action_2 = engine._determine_action(strong_sell_score, confidence=0.8)
        assert action_2 == 'SELL'
        
        # Neutral score should be HOLD
        neutral_score = 0.1
        action_3 = engine._determine_action(neutral_score, confidence=0.5)
        assert action_3 == 'HOLD'
        
        # Low confidence should tend toward HOLD even with strong scores
        low_conf_score = 0.6
        action_4 = engine._determine_action(low_conf_score, confidence=0.3)
        assert action_4 == 'HOLD'  # Low confidence overrides strong score
    
    def test_time_horizon_impact_on_weights(self, engine):
        """Test that different time horizons adjust factor weights appropriately"""
        # Short-term should emphasize technical analysis
        short_weights = engine._get_time_horizon_weights('short')
        assert short_weights['technical'] > short_weights['fundamental']
        assert short_weights['technical'] >= 0.6  # At least 60% technical for short-term
        
        # Long-term should emphasize fundamental analysis
        long_weights = engine._get_time_horizon_weights('long')
        assert long_weights['fundamental'] > long_weights['technical']
        assert long_weights['fundamental'] >= 0.5  # At least 50% fundamental for long-term
        
        # Medium-term should be balanced
        medium_weights = engine._get_time_horizon_weights('medium')
        assert abs(medium_weights['technical'] - medium_weights['fundamental']) <= 0.1
        
        # All weights should sum to 1.0
        for weights in [short_weights, medium_weights, long_weights]:
            assert abs(sum(weights.values()) - 1.0) < 0.001
    
    def test_risk_tolerance_integration(self, engine, mock_stock_data):
        """Test that risk tolerance affects recommendations appropriately"""
        high_risk_stock_data = {
            'var_95': -0.08,  # High VaR (8% potential loss)
            'volatility': 0.45,  # High volatility
            'beta': 1.8  # High beta
        }
        
        # Conservative investors should get smaller position sizes for risky stocks
        conservative_rec = engine._apply_risk_tolerance_adjustments(
            base_recommendation={'position_size': 0.05, 'stop_loss': 2400.0},
            risk_data=high_risk_stock_data,
            risk_tolerance='conservative'
        )
        
        # Aggressive investors should get larger position sizes
        aggressive_rec = engine._apply_risk_tolerance_adjustments(
            base_recommendation={'position_size': 0.05, 'stop_loss': 2400.0},
            risk_data=high_risk_stock_data,
            risk_tolerance='aggressive'
        )
        
        # Conservative should have smaller position size and tighter stop loss
        assert conservative_rec['position_size'] < aggressive_rec['position_size']
        assert conservative_rec['stop_loss'] > aggressive_rec['stop_loss']  # Tighter stop = higher price
    
    def test_get_composited_direction_logic(self, engine):
        """Test the logic for determining composite direction from score."""
        # Positive cases
        assert engine._get_composite_direction(0.5) == 'positive'
        assert engine._get_composite_direction(0.11) == 'positive'
        
        # Negative cases
        assert engine._get_composite_direction(-0.5) == 'negative'
        assert engine._get_composite_direction(-0.11) == 'negative'
        
        # Neutral cases (including boundaries)
        assert engine._get_composite_direction(0.1) == 'neutral'
        assert engine._get_composite_direction(-0.1) == 'neutral'
        assert engine._get_composite_direction(0.0) == 'neutral'
        assert engine._get_composite_direction(0.05) == 'neutral'
        assert engine._get_composite_direction(-0.05) == 'neutral'


    def test_enhanced_recommendation_integration(self, engine, mock_stock_data):
        """Test full enhanced recommendation pipeline integration"""
        # This is an integration test to ensure all components work together
        with patch('src.analysis.indicators.TechnicalIndicators') as mock_tech_indicators, \
             patch('src.analysis.fundamental.FundamentalAnalysis') as mock_fundamental:
            
            # Mock technical indicators
            mock_tech_instance = mock_tech_indicators.return_value
            mock_tech_instance.get_indicator_summary.return_value = {
                'current_price': 2650.0,
                'rsi': 45.0,
                'trend_signal': 'bullish',
                'momentum_signal': 'neutral'
            }
            
            # Mock fundamental analysis
            mock_fund_instance = mock_fundamental.return_value
            mock_fund_instance.get_fundamental_summary.return_value = {
                'pe_ratio': 22.5,
                'roe': 15.2,
                'debt_to_equity': 0.35,
                'revenue_growth_yoy': 8.5
            }
            
            # Generate enhanced recommendation
            recommendation = engine.analyze_stock_enhanced(
                stock_data=mock_stock_data,
                risk_tolerance='moderate',
                time_horizon='medium',
                market_data=None  # Optional
            )
            
            # Verify the enhanced recommendation structure
            assert isinstance(recommendation, dict)
            assert recommendation['symbol'] == 'RELIANCE.BO'
            assert 'enhanced_recommendation' in recommendation
            assert 'factor_breakdown' in recommendation
            assert 'risk_analysis' in recommendation
            
            # Enhanced recommendation should have all required fields
            enhanced_rec = recommendation['enhanced_recommendation']
            assert 'action' in enhanced_rec
            assert 'confidence' in enhanced_rec
            assert 'rationale' in enhanced_rec
            assert 'scenario_analysis' in enhanced_rec


class TestEnhancedRecommendationEngineEdgeCases:
    """Test edge cases and error handling"""
    
    @pytest.fixture
    def engine(self):
        return EnhancedRecommendationEngine()
    
    def test_insufficient_data_handling(self, engine):
        """Test behavior with insufficient data"""
        # Create minimal stock data
        minimal_data = pd.DataFrame({
            'Close': [100, 101],  # Only 2 data points
            'Volume': [1000, 1100],
            'Open': [99, 100],
            'High': [102, 103],
            'Low': [98, 99],
            'Adj Close': [100, 101]
        })
        minimal_stock_data = StockData(symbol="TEST.BO", data=minimal_data, period="2d")
        
        recommendation = engine.analyze_stock_enhanced(minimal_stock_data)
        
        # Should handle gracefully with lower confidence
        assert recommendation['enhanced_recommendation']['confidence'] < 0.5
        assert 'insufficient_data' in recommendation['enhanced_recommendation']['key_risks']
    
    def test_missing_fundamental_data_handling(self, engine, sample_stock_data):
        """Test handling when fundamental data is unavailable"""
        mock_stock_data = StockData(
            symbol="RELIANCE.BO",
            data=sample_stock_data,
            period="1y"
        )
        
        with patch.object(engine.fundamental_scorer, 'calculate_score') as mock_fund:
            # Mock fundamental scorer returning None/empty data
            mock_fund.return_value = {'score': None, 'details': {}, 'data_quality': 'insufficient'}
            
            recommendation = engine.generate_enhanced_recommendation(mock_stock_data)
            
            # Should still generate recommendation with technical/risk focus
            assert recommendation is not None
            assert recommendation['action'] in ['BUY', 'SELL', 'HOLD']
            # Confidence should be reduced due to missing fundamental data
            assert recommendation['confidence'] < 0.7