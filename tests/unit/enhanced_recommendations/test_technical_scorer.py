import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch
from src.engine.technical_scorer import TechnicalScorer
from src.data.yahoo_client import StockData

class TestTechnicalScorer:
    """TDD tests for Technical Scorer - Phase 2A Enhancement"""
    
    @pytest.fixture
    def scorer(self):
        return TechnicalScorer()
    
    @pytest.fixture
    def mock_stock_data(self, sample_stock_data):
        return StockData(
            symbol="RELIANCE.BO",
            data=sample_stock_data,
            period="1y"
        )
    
    @pytest.fixture
    def mock_technical_indicators(self):
        """Mock technical indicators from existing TechnicalIndicators class"""
        return {
            'current_price': 2650.0,
            'sma_20': 2620.0,
            'sma_50': 2580.0,
            'sma_200': 2500.0,
            'ema_12': 2635.0,
            'ema_26': 2610.0,
            'rsi': 45.0,
            'macd': 15.2,
            'macd_signal': 12.8,
            'macd_histogram': 2.4,
            'bollinger_upper': 2720.0,
            'bollinger_lower': 2580.0,
            'bollinger_percent_b': 0.35,
            'bb_squeeze': False,
            'bb_breakout': True,
            'trend_signal': 'bullish',
            'momentum_signal': 'neutral',
            'volume_signal': 'increasing',
            'relative_volume': 1.25,
            'support_levels': [2580.0, 2520.0, 2450.0],
            'resistance_levels': [2720.0, 2780.0, 2850.0],
            'pattern_detected': 'ascending_triangle'
        }
    
    # TDD Cycle 2: Technical Score Enhancement Tests
    
    def test_technical_scorer_initialization(self, scorer):
        """Test TechnicalScorer initializes with proper configuration"""
        assert scorer is not None
        assert hasattr(scorer, 'calculate_score')
        assert hasattr(scorer, 'indicator_weights')
        assert hasattr(scorer, 'pattern_weights')
        
        # Should have predefined indicator weights
        expected_indicators = [
            'trend', 'momentum', 'volume', 'support_resistance', 
            'bollinger_bands', 'pattern_recognition', 'mean_reversion'
        ]
        for indicator in expected_indicators:
            assert indicator in scorer.indicator_weights
        
        # Weights should sum to 1.0
        assert abs(sum(scorer.indicator_weights.values()) - 1.0) < 0.001
    
    def test_enhanced_technical_scoring(self, scorer, mock_stock_data, mock_technical_indicators):
        """Test enhanced technical scoring with multiple indicator integration"""
        with patch('src.analysis.indicators.TechnicalIndicators') as mock_tech:
            mock_tech_instance = mock_tech.return_value
            mock_tech_instance.get_indicator_summary.return_value = mock_technical_indicators
            mock_tech_instance.detect_support_resistance.return_value = {
                'support_levels': [2580.0, 2520.0],
                'resistance_levels': [2720.0, 2780.0],
                'support_strength': [0.8, 0.6],
                'resistance_strength': [0.9, 0.7]
            }
            
            result = scorer.calculate_score(mock_stock_data)
            
            # Should return structured score with breakdown
            assert isinstance(result, dict)
            assert 'score' in result
            assert 'details' in result
            assert 'component_scores' in result
            
            # Score should be normalized to -1.0 to +1.0 range
            assert -1.0 <= result['score'] <= 1.0
            
            # Should include all component scores
            expected_components = [
                'trend_score', 'momentum_score', 'volume_score', 
                'support_resistance_score', 'bollinger_score', 'pattern_score'
            ]
            for component in expected_components:
                assert component in result['component_scores']
    
    def test_trend_analysis_scoring(self, scorer, mock_technical_indicators):
        """Test trend analysis component scoring"""
        trend_score = scorer._calculate_trend_score(mock_technical_indicators)
        
        assert isinstance(trend_score, dict)
        assert 'score' in trend_score
        assert 'details' in trend_score
        
        # With bullish trend signal, should have positive score
        assert trend_score['score'] > 0
        
        # Details should include SMA analysis
        details = trend_score['details']
        assert 'sma_alignment' in details
        assert 'price_vs_sma_200' in details
        assert 'trend_strength' in details
        
        # Price above SMA 200 should contribute positively
        current_price = mock_technical_indicators['current_price']
        sma_200 = mock_technical_indicators['sma_200']
        if current_price > sma_200:
            assert details['price_vs_sma_200'] > 0
    
    def test_momentum_analysis_scoring(self, scorer, mock_technical_indicators):
        """Test momentum analysis component scoring"""
        momentum_score = scorer._calculate_momentum_score(mock_technical_indicators)
        
        assert isinstance(momentum_score, dict)
        assert 'score' in momentum_score
        assert 'details' in momentum_score
        
        # Score should be in valid range
        assert -1.0 <= momentum_score['score'] <= 1.0
        
        # Details should include RSI and MACD analysis
        details = momentum_score['details']
        assert 'rsi_score' in details
        assert 'macd_score' in details
        assert 'momentum_convergence' in details
        
        # RSI of 45 (neutral zone) should have moderate scoring
        rsi = mock_technical_indicators['rsi']
        if 30 < rsi < 70:  # Neutral zone
            assert abs(details['rsi_score']) < 0.5
    
    def test_volume_confirmation_scoring(self, scorer, mock_technical_indicators):
        """Test volume confirmation in scoring"""
        volume_score = scorer._calculate_volume_score(mock_technical_indicators)
        
        assert isinstance(volume_score, dict)
        assert 'score' in volume_score
        assert 'details' in volume_score
        
        # High relative volume should boost score
        relative_volume = mock_technical_indicators['relative_volume']
        if relative_volume > 1.2:
            assert volume_score['score'] > 0.3
        
        # Details should include volume analysis
        details = volume_score['details']
        assert 'relative_volume_score' in details
        assert 'volume_trend' in details
        assert 'volume_confirmation' in details
    
    def test_support_resistance_impact(self, scorer, mock_technical_indicators):
        """Test support and resistance level impact on scoring"""
        # Mock support/resistance data
        sr_data = {
            'support_levels': [2580.0, 2520.0, 2450.0],
            'resistance_levels': [2720.0, 2780.0, 2850.0],
            'support_strength': [0.8, 0.6, 0.4],
            'resistance_strength': [0.9, 0.7, 0.5]
        }
        
        sr_score = scorer._calculate_support_resistance_score(
            mock_technical_indicators, 
            sr_data
        )
        
        assert isinstance(sr_score, dict)
        assert 'score' in sr_score
        assert 'details' in sr_score
        
        # Current price position relative to support/resistance should affect score
        current_price = mock_technical_indicators['current_price']
        nearest_resistance = min([r for r in sr_data['resistance_levels'] if r > current_price])
        nearest_support = max([s for s in sr_data['support_levels'] if s < current_price])
        
        # Should calculate distance to key levels
        details = sr_score['details']
        assert 'distance_to_resistance' in details
        assert 'distance_to_support' in details
        assert 'support_strength_score' in details
        assert 'resistance_strength_score' in details
    
    def test_bollinger_band_weight_in_score(self, scorer, mock_technical_indicators):
        """Test Bollinger Band analysis integration in scoring"""
        bb_score = scorer._calculate_bollinger_score(mock_technical_indicators)
        
        assert isinstance(bb_score, dict)
        assert 'score' in bb_score
        assert 'details' in bb_score
        
        # Bollinger %B should influence score
        percent_b = mock_technical_indicators['bollinger_percent_b']
        details = bb_score['details']
        
        assert 'percent_b_score' in details
        assert 'squeeze_analysis' in details
        assert 'breakout_analysis' in details
        
        # %B of 0.35 (below midline) should have specific interpretation
        if percent_b < 0.5:
            assert details['percent_b_interpretation'] == 'below_midline'
        
        # Bollinger squeeze should be identified
        if mock_technical_indicators['bb_squeeze']:
            assert details['squeeze_analysis']['is_squeezed'] == True
    
    def test_pattern_recognition_integration(self, scorer, mock_technical_indicators):
        """Test chart pattern recognition integration"""
        pattern_score = scorer._calculate_pattern_score(mock_technical_indicators)
        
        assert isinstance(pattern_score, dict)
        assert 'score' in pattern_score
        assert 'details' in pattern_score
        
        # Should recognize the ascending triangle pattern
        pattern_detected = mock_technical_indicators['pattern_detected']
        details = pattern_score['details']
        
        assert 'pattern_type' in details
        assert 'pattern_reliability' in details
        assert 'pattern_implications' in details
        
        # Ascending triangle should be bullish
        if pattern_detected == 'ascending_triangle':
            assert pattern_score['score'] > 0
            assert details['pattern_implications'] == 'bullish'
    
    def test_mean_reversion_vs_momentum_detection(self, scorer):
        """Test detection of mean reversion vs momentum regimes"""
        # Momentum scenario: trending with volume confirmation
        momentum_indicators = {
            'current_price': 2700.0,
            'sma_20': 2650.0,
            'sma_50': 2600.0,
            'sma_200': 2500.0,  # Strong uptrend
            'rsi': 65.0,        # Not overbought
            'relative_volume': 1.8,  # High volume
            'bollinger_percent_b': 0.75  # Near upper band but not extreme
        }
        
        momentum_regime = scorer._detect_market_regime(momentum_indicators)
        assert momentum_regime['regime'] == 'momentum'
        assert momentum_regime['confidence'] > 0.7
        
        # Mean reversion scenario: extreme RSI with low volume
        reversion_indicators = {
            'current_price': 2600.0,
            'sma_20': 2605.0,
            'sma_50': 2603.0,
            'sma_200': 2598.0,  # Sideways trend
            'rsi': 82.0,        # Extreme overbought
            'relative_volume': 0.6,  # Low volume
            'bollinger_percent_b': 0.95  # Very near upper band
        }
        
        reversion_regime = scorer._detect_market_regime(reversion_indicators)
        assert reversion_regime['regime'] == 'mean_reversion'
        assert reversion_regime['confidence'] > 0.6
    
    def test_multi_timeframe_consistency_check(self, scorer):
        """Test consistency across multiple timeframes"""
        # Mock indicators for different timeframes
        short_term_indicators = {
            'trend_signal': 'bullish',
            'momentum_signal': 'bullish',
            'timeframe': 'short'
        }
        
        medium_term_indicators = {
            'trend_signal': 'bearish',
            'momentum_signal': 'neutral',
            'timeframe': 'medium'
        }
        
        consistency = scorer._check_timeframe_consistency(
            [short_term_indicators, medium_term_indicators]
        )
        
        assert isinstance(consistency, dict)
        assert 'alignment_score' in consistency
        assert 'conflicts' in consistency
        assert 'dominant_direction' in consistency
        
        # Conflicting signals should reduce alignment score
        assert consistency['alignment_score'] < 0.5
        assert len(consistency['conflicts']) > 0
    
    def test_adaptive_scoring_weights(self, scorer):
        """Test that scoring weights adapt to market conditions"""
        # In high volatility, mean reversion should get higher weight
        high_vol_conditions = {'market_volatility': 'high', 'regime': 'mean_reversion'}
        adaptive_weights_1 = scorer._get_adaptive_weights(high_vol_conditions)
        
        # In trending markets, momentum should get higher weight
        trending_conditions = {'market_volatility': 'normal', 'regime': 'momentum'}
        adaptive_weights_2 = scorer._get_adaptive_weights(trending_conditions)
        
        # Verify adaptation
        assert adaptive_weights_1['mean_reversion'] > scorer.indicator_weights['mean_reversion']
        assert adaptive_weights_2['momentum'] > scorer.indicator_weights['momentum']
        
        # Weights should still sum to 1.0
        assert abs(sum(adaptive_weights_1.values()) - 1.0) < 0.001
        assert abs(sum(adaptive_weights_2.values()) - 1.0) < 0.001


class TestTechnicalScorerEdgeCases:
    """Test edge cases and error handling for TechnicalScorer"""
    
    @pytest.fixture
    def scorer(self):
        return TechnicalScorer()
    
    def test_insufficient_technical_data_handling(self, scorer):
        """Test handling when technical indicators are incomplete"""
        incomplete_indicators = {
            'current_price': 2650.0,
            'rsi': 45.0,
            # Missing most other indicators
        }
        
        # Mock stock data with insufficient data
        minimal_data = pd.DataFrame({
            'Close': [100, 102, 101],  # Only 3 data points
            'Volume': [1000, 1100, 1050],
            'Open': [99, 100, 100],
            'High': [102, 103, 102],
            'Low': [98, 99, 100],
            'Adj Close': [100, 102, 101]
        })
        minimal_stock_data = StockData(symbol="TEST.BO", data=minimal_data, period="3d")
        
        result = scorer.calculate_score(minimal_stock_data)
        
        # Should handle gracefully
        assert result is not None
        assert 'score' in result
        assert result['details']['data_quality'] == 'insufficient'
        assert result['score'] is None or abs(result['score']) < 0.3  # Low confidence due to insufficient data
    
    def test_extreme_indicator_values_handling(self, scorer):
        """Test handling of extreme technical indicator values"""
        extreme_indicators = {
            'current_price': 1000.0,
            'rsi': 110.0,     # Invalid RSI (should be 0-100)
            'macd': -1000.0,  # Extreme MACD value
            'bollinger_percent_b': 2.0,  # %B above 1.0 (breakout)
            'relative_volume': -5.0,     # Invalid negative volume
        }
        
        # Should sanitize extreme values
        sanitized = scorer._sanitize_indicators(extreme_indicators)
        
        assert 0 <= sanitized['rsi'] <= 100
        assert sanitized['bollinger_percent_b'] >= 0.0
        assert sanitized['relative_volume'] >= 0.0
        assert abs(sanitized['macd']) < 100  # Should cap extreme values
    
    def test_missing_pattern_data_handling(self, scorer, sample_stock_data):
        """Test behavior when pattern recognition fails"""
        from src.data.yahoo_client import StockData
        mock_stock_data = StockData(
            symbol="RELIANCE.BO",
            data=sample_stock_data,
            period="1y"
        )
        
        indicators_no_pattern = {
            'current_price': 2650.0,
            'trend_signal': 'neutral',
            'pattern_detected': None,  # No pattern detected
        }
        
        with patch('src.analysis.indicators.TechnicalIndicators') as mock_tech:
            mock_tech_instance = mock_tech.return_value
            mock_tech_instance.get_indicator_summary.return_value = indicators_no_pattern
            
            result = scorer.calculate_score(mock_stock_data)
            
            # Should still return valid score without pattern component
            assert result is not None
            assert result['component_scores']['pattern_score']['score'] == 0.0
            assert 'no_pattern_detected' in result['details']