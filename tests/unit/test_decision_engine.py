import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch
from src.engine.decision_engine import DecisionEng<PERSON>, RecommendationScorer, RiskAssessment
from src.data.yahoo_client import StockData
from src.analysis.indicators import TechnicalIndicators

class TestDecisionEngine:
    
    @pytest.fixture
    def engine(self):
        return DecisionEngine()
    
    @pytest.fixture
    def mock_stock_data(self, sample_stock_data):
        return StockData(
            symbol="RELIANCE.BO",
            data=sample_stock_data,
            period="1y"
        )
    
    def test_engine_initialization(self, engine):
        """Test that DecisionEngine initializes properly"""
        assert engine is not None
        assert hasattr(engine, 'analyze_stock')
        assert hasattr(engine, 'generate_recommendation')
    
    def test_basic_stock_analysis(self, engine, mock_stock_data):
        """Test basic stock analysis workflow"""
        with patch.object(TechnicalIndicators, 'get_indicator_summary') as mock_indicators:
            mock_indicators.return_value = {
                'current_price': 2654.50,
                'sma_20': 2620.0,
                'rsi': 42.5,
                'trend_signal': 'bullish',
                'momentum_signal': 'bullish'
            }
            
            result = engine.analyze_stock(mock_stock_data)
            
            assert isinstance(result, dict)
            assert 'recommendation' in result
            assert 'confidence' in result
            assert 'technical_analysis' in result
            assert result['symbol'] == 'RELIANCE.BO'
    
    def test_buy_recommendation_logic(self, engine):
        """Test buy recommendation generation"""
        bullish_indicators = {
            'current_price': 100.0,
            'sma_20': 95.0,
            'sma_50': 90.0,
            'rsi': 45.0,
            'macd_crossover': 1,
            'trend_signal': 'bullish',
            'momentum_signal': 'bullish'
        }
        
        recommendation = engine.generate_recommendation(bullish_indicators)
        
        assert recommendation['action'] == 'BUY'
        assert recommendation['confidence'] > 0.5
        assert recommendation['target_price'] > 100.0
    
    def test_sell_recommendation_logic(self, engine):
        """Test sell recommendation generation"""
        bearish_indicators = {
            'current_price': 100.0,
            'sma_20': 105.0,
            'sma_50': 110.0,
            'rsi': 75.0,
            'macd_crossover': -1,
            'trend_signal': 'bearish',
            'momentum_signal': 'bearish'
        }
        
        recommendation = engine.generate_recommendation(bearish_indicators)
        
        assert recommendation['action'] == 'SELL'
        assert recommendation['confidence'] > 0.5
        assert recommendation['target_price'] < 100.0
    
    def test_hold_recommendation_logic(self, engine):
        """Test hold recommendation generation"""
        neutral_indicators = {
            'current_price': 100.0,
            'sma_20': 99.0,
            'sma_50': 101.0,
            'rsi': 50.0,
            'macd_crossover': 0,
            'trend_signal': 'neutral',
            'momentum_signal': 'neutral'
        }
        
        recommendation = engine.generate_recommendation(neutral_indicators)
        
        assert recommendation['action'] == 'HOLD'
        assert 0.3 <= recommendation['confidence'] <= 0.7  # Moderate confidence for hold
    
    def test_risk_adjusted_recommendations(self, engine):
        """Test risk tolerance adjustment in recommendations"""
        indicators = {
            'current_price': 100.0,
            'rsi': 45.0,
            'trend_signal': 'bullish',
            'momentum_signal': 'bullish'
        }
        
        conservative_rec = engine.generate_recommendation(indicators, risk_tolerance='conservative')
        aggressive_rec = engine.generate_recommendation(indicators, risk_tolerance='aggressive')
        
        # Conservative should have lower confidence, higher stop loss
        assert conservative_rec['confidence'] <= aggressive_rec['confidence']
        assert conservative_rec['stop_loss'] >= aggressive_rec['stop_loss']
    
    def test_confidence_calculation(self, engine):
        """Test confidence score calculation logic"""
        # Strong bullish signals
        strong_signals = {
            'trend_signal': 'bullish',
            'momentum_signal': 'bullish',
            'rsi': 45.0,  # Not overbought
            'macd_crossover': 1,
            'relative_volume': 1.5  # Above average volume
        }
        
        # Weak signals
        weak_signals = {
            'trend_signal': 'neutral',
            'momentum_signal': 'neutral', 
            'rsi': 50.0,
            'macd_crossover': 0,
            'relative_volume': 0.8
        }
        
        strong_confidence = engine._calculate_confidence(strong_signals)
        weak_confidence = engine._calculate_confidence(weak_signals)
        
        assert strong_confidence > weak_confidence
        assert 0.0 <= strong_confidence <= 1.0
        assert 0.0 <= weak_confidence <= 1.0
    
    def test_target_price_calculation(self, engine):
        """Test target price calculation"""
        indicators = {
            'current_price': 100.0,
            'sma_20': 95.0,
            'bb_upper': 110.0,
            'bb_lower': 85.0
        }
        
        buy_target = engine._calculate_target_price(indicators, 'BUY')
        sell_target = engine._calculate_target_price(indicators, 'SELL')
        
        assert buy_target > 100.0  # Buy target should be above current price
        assert sell_target < 100.0  # Sell target should be below current price
    
    def test_stop_loss_calculation(self, engine):
        """Test stop loss calculation"""
        indicators = {
            'current_price': 100.0,
            'sma_20': 95.0,
            'bb_lower': 85.0
        }
        
        buy_stop_loss = engine._calculate_stop_loss(indicators, 'BUY', risk_tolerance='moderate')
        conservative_stop_loss = engine._calculate_stop_loss(indicators, 'BUY', risk_tolerance='conservative')
        aggressive_stop_loss = engine._calculate_stop_loss(indicators, 'BUY', risk_tolerance='aggressive')
        
        # All should be below current price for BUY recommendation
        assert buy_stop_loss < 100.0
        assert conservative_stop_loss >= aggressive_stop_loss  # Conservative has tighter stop loss
    
    def test_insufficient_data_handling(self, engine):
        """Test handling of insufficient data"""
        incomplete_indicators = {
            'current_price': 100.0,
            # Missing most indicators
        }
        
        recommendation = engine.generate_recommendation(incomplete_indicators)
        
        assert recommendation['action'] == 'HOLD'  # Default to hold with insufficient data
        assert recommendation['confidence'] < 0.5  # Low confidence
    
    def test_extreme_rsi_handling(self, engine):
        """Test handling of extreme RSI values"""
        overbought_indicators = {
            'current_price': 100.0,
            'rsi': 85.0,  # Extremely overbought
            'trend_signal': 'bullish'
        }
        
        oversold_indicators = {
            'current_price': 100.0,
            'rsi': 15.0,  # Extremely oversold
            'trend_signal': 'bearish'
        }
        
        overbought_rec = engine.generate_recommendation(overbought_indicators)
        oversold_rec = engine.generate_recommendation(oversold_indicators)
        
        # Extreme RSI should reduce confidence or flip recommendation
        assert overbought_rec['confidence'] < 0.8 or overbought_rec['action'] != 'BUY'
        assert oversold_rec['confidence'] < 0.8 or oversold_rec['action'] != 'SELL'

class TestRecommendationScorer:
    
    @pytest.fixture
    def scorer(self):
        return RecommendationScorer()
    
    def test_trend_scoring(self, scorer):
        """Test trend-based scoring"""
        bullish_score = scorer.score_trend_signals({
            'trend_signal': 'bullish',
            'sma_20': 100.0,
            'sma_50': 95.0,
            'current_price': 105.0
        })
        
        bearish_score = scorer.score_trend_signals({
            'trend_signal': 'bearish',
            'sma_20': 100.0,
            'sma_50': 105.0,
            'current_price': 95.0
        })
        
        assert bullish_score > 0.5
        assert bearish_score < -0.5
        assert -1.0 <= bullish_score <= 1.0
        assert -1.0 <= bearish_score <= 1.0
    
    def test_momentum_scoring(self, scorer):
        """Test momentum-based scoring"""
        bullish_momentum = scorer.score_momentum_signals({
            'rsi': 45.0,
            'macd_crossover': 1,
            'momentum_signal': 'bullish'
        })
        
        bearish_momentum = scorer.score_momentum_signals({
            'rsi': 75.0,
            'macd_crossover': -1,
            'momentum_signal': 'bearish'
        })
        
        assert bullish_momentum > 0
        assert bearish_momentum < 0
    
    def test_volume_scoring(self, scorer):
        """Test volume-based scoring"""
        high_volume_score = scorer.score_volume_signals({
            'relative_volume': 2.0,  # Double average volume
            'volume_sma': 1000000
        })
        
        low_volume_score = scorer.score_volume_signals({
            'relative_volume': 0.5,  # Half average volume
            'volume_sma': 1000000
        })
        
        assert high_volume_score > low_volume_score
        assert high_volume_score > 0.5  # High volume should boost confidence
    
    def test_combined_score_calculation(self, scorer):
        """Test combined score from all factors"""
        strong_bullish_indicators = {
            'trend_signal': 'bullish',
            'momentum_signal': 'bullish',
            'current_price': 100.0,
            'sma_20': 95.0,
            'rsi': 45.0,
            'macd_crossover': 1,
            'relative_volume': 1.5
        }
        
        combined_score = scorer.calculate_combined_score(strong_bullish_indicators)
        
        assert combined_score > 0.5  # Should be strongly bullish
        assert -1.0 <= combined_score <= 1.0

class TestRiskAssessment:
    
    @pytest.fixture
    def risk_assessor(self):
        return RiskAssessment()
    
    def test_volatility_assessment(self, risk_assessor, sample_stock_data):
        """Test volatility-based risk assessment"""
        mock_stock_data = StockData("TEST.BO", sample_stock_data, "1y")
        
        risk_metrics = risk_assessor.assess_risk(mock_stock_data)
        
        assert 'volatility' in risk_metrics
        assert 'max_drawdown' in risk_metrics
        assert 'risk_level' in risk_metrics
        assert risk_metrics['risk_level'] in ['LOW', 'MEDIUM', 'HIGH']
    
    def test_risk_level_classification(self, risk_assessor):
        """Test risk level classification"""
        low_volatility = {'daily_volatility': 0.01, 'max_drawdown': 0.05}  # 1% daily, 5% max drawdown
        high_volatility = {'daily_volatility': 0.05, 'max_drawdown': 0.25}  # 5% daily, 25% max drawdown
        
        low_risk = risk_assessor._classify_risk_level(low_volatility)
        high_risk = risk_assessor._classify_risk_level(high_volatility)
        
        assert low_risk == 'LOW'
        assert high_risk == 'HIGH'
    
    def test_sharpe_ratio_calculation(self, risk_assessor, sample_stock_data):
        """Test Sharpe ratio calculation"""
        returns = sample_stock_data['Close'].pct_change().dropna()
        sharpe = risk_assessor._calculate_sharpe_ratio(returns)
        
        assert isinstance(sharpe, float)
        assert not pd.isna(sharpe)
    
    def test_max_drawdown_calculation(self, risk_assessor, sample_stock_data):
        """Test maximum drawdown calculation"""
        prices = sample_stock_data['Close']
        max_dd = risk_assessor._calculate_max_drawdown(prices)
        
        assert isinstance(max_dd, float)
        assert max_dd <= 0  # Drawdown should be negative or zero
        assert max_dd >= -1  # Shouldn't exceed -100%
    
    def test_beta_calculation(self, risk_assessor, sample_stock_data):
        """Test beta calculation against market"""
        # Mock market data (Nifty 50 equivalent)
        market_data = sample_stock_data['Close'] * 0.8 + np.random.normal(0, 5, len(sample_stock_data))
        
        beta = risk_assessor._calculate_beta(sample_stock_data['Close'], market_data)
        
        assert isinstance(beta, float)
        assert beta > 0  # Should be positive for most stocks

class TestEngineIntegration:
    
    def test_end_to_end_analysis(self, sample_stock_data):
        """Test complete end-to-end analysis workflow"""
        engine = DecisionEngine()
        stock_data = StockData("RELIANCE.BO", sample_stock_data, "1y")
        
        with patch.object(TechnicalIndicators, 'get_indicator_summary') as mock_indicators:
            mock_indicators.return_value = {
                'current_price': 2654.50,
                'sma_20': 2620.0,
                'sma_50': 2580.0,
                'rsi': 42.5,
                'macd_crossover': 1,
                'trend_signal': 'bullish',
                'momentum_signal': 'bullish',
                'relative_volume': 1.4
            }
            
            result = engine.analyze_stock(stock_data, risk_tolerance='moderate')
            
            # Check complete result structure
            assert 'symbol' in result
            assert 'recommendation' in result
            assert 'confidence' in result
            assert 'technical_analysis' in result
            assert 'risk_assessment' in result
            
            # Check recommendation structure
            rec = result['recommendation']
            assert 'action' in rec
            assert 'confidence' in rec
            assert 'target_price' in rec
            assert 'stop_loss' in rec
            assert 'risk_level' in rec
    
    def test_error_handling_in_analysis(self):
        """Test error handling in analysis workflow"""
        engine = DecisionEngine()
        
        # Test with None data
        with pytest.raises(ValueError):
            engine.analyze_stock(None)
        
        # Test with invalid risk tolerance
        mock_data = Mock()
        mock_data.symbol = "TEST.BO"
        
        with pytest.raises(ValueError):
            engine.analyze_stock(mock_data, risk_tolerance='invalid')
    
    def test_different_risk_tolerances(self, sample_stock_data):
        """Test analysis with different risk tolerances"""
        engine = DecisionEngine()
        stock_data = StockData("TEST.BO", sample_stock_data, "1y")
        
        with patch.object(TechnicalIndicators, 'get_indicator_summary') as mock_indicators:
            mock_indicators.return_value = {
                'current_price': 100.0,
                'trend_signal': 'bullish',
                'momentum_signal': 'bullish'
            }
            
            conservative = engine.analyze_stock(stock_data, risk_tolerance='conservative')
            moderate = engine.analyze_stock(stock_data, risk_tolerance='moderate')
            aggressive = engine.analyze_stock(stock_data, risk_tolerance='aggressive')
            
            # Conservative should have lower target prices and tighter stop losses
            if conservative['recommendation']['action'] == 'BUY':
                assert conservative['recommendation']['stop_loss'] >= aggressive['recommendation']['stop_loss']

class TestSupportResistanceIntegration:
    """Test integration of support/resistance with decision engine"""
    
    @pytest.fixture
    def engine(self):
        return DecisionEngine()
    
    @pytest.fixture
    def mock_stock_data(self, sample_stock_data):
        return StockData(
            symbol="RELIANCE.BO",
            data=sample_stock_data,
            period="1y"
        )
    
    def test_support_resistance_in_recommendation(self, engine, mock_stock_data):
        """Test that support/resistance levels influence recommendations"""
        with patch.object(TechnicalIndicators, 'detect_support_resistance') as mock_sr:
            mock_sr.return_value = {
                'support_levels': [2500.0, 2400.0],
                'resistance_levels': [2700.0, 2800.0],
                'support_strength': [0.8, 0.6],
                'resistance_strength': [0.9, 0.7]
            }
            
            with patch.object(TechnicalIndicators, 'get_indicator_summary') as mock_indicators:
                mock_indicators.return_value = {
                    'current_price': 2600.0,  # Between support and resistance
                    'sma_20': 2550.0,
                    'rsi': 50.0,
                    'trend_signal': 'neutral',
                    'momentum_signal': 'neutral'
                }
                
                result = engine.analyze_stock(mock_stock_data)
                
                # Should have support/resistance data in result
                assert 'support_levels' in result
                assert 'resistance_levels' in result
                
                # Target price should consider resistance levels
                target_price = result['recommendation'].get('target_price', 0)
                if target_price > 2600:  # If buy recommendation
                    assert target_price <= 2700.0  # Should not exceed first resistance


class TestSMA200Integration:
    """Test integration of 200-day SMA in trend analysis and decision making"""
    
    @pytest.fixture
    def engine(self):
        return DecisionEngine()
    
    @pytest.fixture
    def mock_stock_data(self, sample_stock_data):
        return StockData(
            symbol="RELIANCE.BO",
            data=sample_stock_data,
            period="1y"
        )
    
    def test_sma200_trend_analysis(self, engine, mock_stock_data):
        """Test that 200-day SMA influences trend signals"""
        with patch.object(TechnicalIndicators, 'get_indicator_summary') as mock_indicators:
            # Test price above SMA-200 (long-term bullish)
            mock_indicators.return_value = {
                'current_price': 2800.0,
                'sma_20': 2750.0,
                'sma_50': 2700.0,
                'sma_200': 2500.0,  # Price well above SMA-200
                'rsi': 50.0,
                'trend_signal': 'bullish',
                'momentum_signal': 'neutral'
            }
            
            result_bullish = engine.analyze_stock(mock_stock_data)
            
            # Test price below SMA-200 (long-term bearish)
            mock_indicators.return_value = {
                'current_price': 2300.0,
                'sma_20': 2350.0,
                'sma_50': 2400.0,
                'sma_200': 2600.0,  # Price well below SMA-200
                'rsi': 50.0,
                'trend_signal': 'bearish',
                'momentum_signal': 'neutral'
            }
            
            result_bearish = engine.analyze_stock(mock_stock_data)
            
            # When using SMA-200 in confidence calculation:
            # Bullish long-term trend should have higher confidence than bearish
            # This test will fail until SMA-200 logic is implemented
            bullish_conf = result_bullish['confidence']
            bearish_conf = result_bearish['confidence'] 
            
            # SMA-200 trend alignment should boost confidence
            # Currently this may be equal, should become different after implementation
            assert abs(bullish_conf - bearish_conf) > 0.05  # Should have meaningful difference
    
    def test_sma200_in_recommendation_logic(self, engine, mock_stock_data):
        """Test SMA-200 affects recommendation confidence"""
        with patch.object(TechnicalIndicators, 'get_indicator_summary') as mock_indicators:
            # Strong bullish setup with SMA-200 confirmation
            mock_indicators.return_value = {
                'current_price': 2800.0,
                'sma_20': 2750.0,
                'sma_50': 2700.0,
                'sma_200': 2500.0,  # All SMAs aligned bullishly
                'rsi': 45.0,  # Not overbought
                'trend_signal': 'bullish',
                'momentum_signal': 'bullish',
                'relative_volume': 1.2
            }
            
            result = engine.analyze_stock(mock_stock_data)
            
            # Should have high confidence with SMA-200 trend confirmation
            # This tests that SMA-200 alignment gives confidence boost
            assert result['confidence'] >= 0.65  # Higher threshold for SMA-200 boost
            
            # SMA-200 should be in technical analysis
            assert 'sma_200' in result
            assert result['sma_200'] == 2500.0
    
    def test_sma200_trend_divergence_warning(self, engine, mock_stock_data):
        """Test handling when short-term and long-term trends diverge"""
        with patch.object(TechnicalIndicators, 'get_indicator_summary') as mock_indicators:
            # Short-term bullish but long-term bearish
            mock_indicators.return_value = {
                'current_price': 2400.0,
                'sma_20': 2350.0,  # Above short-term SMA
                'sma_50': 2300.0,  # Above medium-term SMA  
                'sma_200': 2600.0,  # Below long-term SMA
                'rsi': 50.0,
                'trend_signal': 'bullish',  # Short-term bullish
                'momentum_signal': 'neutral'
            }
            
            result = engine.analyze_stock(mock_stock_data)
            
            # Conflicting signals should reduce confidence
            assert result['confidence'] < 0.7  # Lower confidence due to divergence

    # Bollinger Band Integration Tests - TDD for Micro-Phase 1C
    
    def test_bollinger_percent_b_overbought_oversold_signals(self, engine, mock_stock_data):
        """Test that %B values influence overbought/oversold detection"""
        with patch.object(TechnicalIndicators, 'get_indicator_summary') as mock_indicators:
            # Test overbought scenario: %B > 0.8 (price near upper band)
            mock_indicators.return_value = {
                'current_price': 2900.0,
                'sma_20': 2800.0,
                'rsi': 45.0,  # Neutral RSI
                'bb_upper': 2950.0,
                'bb_lower': 2650.0,
                'bb_middle': 2800.0,
                'bollinger_percent_b': 0.83,  # Near upper band (overbought)
                'trend_signal': 'bullish',
                'momentum_signal': 'neutral'
            }
            
            result_overbought = engine.analyze_stock(mock_stock_data)
            
            # Test oversold scenario: %B < 0.2 (price near lower band) 
            mock_indicators.return_value = {
                'current_price': 2700.0,
                'sma_20': 2800.0,
                'rsi': 55.0,  # Neutral RSI
                'bb_upper': 2950.0, 
                'bb_lower': 2650.0,
                'bb_middle': 2800.0,
                'bollinger_percent_b': 0.17,  # Near lower band (oversold)
                'trend_signal': 'bearish',
                'momentum_signal': 'neutral'
            }
            
            result_oversold = engine.analyze_stock(mock_stock_data)
            
            # Overbought condition should reduce bullish confidence or suggest caution
            # Oversold condition should reduce bearish confidence or suggest opportunity
            # This test will initially fail until BB integration is implemented
            assert isinstance(result_overbought['confidence'], float)
            assert isinstance(result_oversold['confidence'], float)
            
            # After implementation, we expect:
            # - Overbought + bullish trend = reduced confidence due to potential reversal risk
            # - Oversold + bearish trend = potential buying opportunity (different handling)
    
    def test_bollinger_squeeze_breakout_confidence_boost(self, engine, mock_stock_data):
        """Test that Bollinger squeeze detection affects confidence scoring"""
        with patch.object(TechnicalIndicators, 'get_indicator_summary') as mock_indicators:
            # Test during bollinger squeeze (low volatility, potential breakout)
            mock_indicators.return_value = {
                'current_price': 2800.0,
                'sma_20': 2790.0,
                'rsi': 50.0,
                'bollinger_squeeze': {
                    'is_squeeze': True,
                    'squeeze_intensity': 0.8,
                    'squeeze_periods': 12
                },
                'trend_signal': 'bullish',
                'momentum_signal': 'bullish',
                'relative_volume': 1.5  # Higher volume suggests breakout
            }
            
            result_squeeze = engine.analyze_stock(mock_stock_data)
            
            # Test normal volatility (no squeeze)
            mock_indicators.return_value = {
                'current_price': 2800.0,
                'sma_20': 2790.0,
                'rsi': 50.0,
                'bollinger_squeeze': {
                    'is_squeeze': False,
                    'squeeze_intensity': 0.2,
                    'squeeze_periods': 2
                },
                'trend_signal': 'bullish',
                'momentum_signal': 'bullish',
                'relative_volume': 1.5
            }
            
            result_normal = engine.analyze_stock(mock_stock_data)
            
            # Bollinger squeeze with aligned signals and volume should boost confidence
            # This suggests a higher probability breakout setup
            # Test will initially fail until squeeze integration is implemented
            assert isinstance(result_squeeze['confidence'], float)
            assert isinstance(result_normal['confidence'], float)
            
            # After implementation: squeeze setup should have higher confidence
            # assert result_squeeze['confidence'] > result_normal['confidence']
    
    def test_bollinger_breakout_confirmation_signals(self, engine, mock_stock_data):
        """Test Bollinger Band breakout confirmation in decision logic"""
        with patch.object(TechnicalIndicators, 'get_indicator_summary') as mock_indicators:
            # Test bullish breakout above upper band
            mock_indicators.return_value = {
                'current_price': 2950.0,  # Above upper band
                'bb_upper': 2930.0,
                'bb_lower': 2650.0,
                'bb_middle': 2790.0,
                'bollinger_breakout': {
                    'upper_breakout': True,
                    'lower_breakout': False,
                    'breakout_strength': 0.7
                },
                'trend_signal': 'bullish',
                'momentum_signal': 'bullish',
                'relative_volume': 2.1  # High volume confirms breakout
            }
            
            result_bullish_breakout = engine.analyze_stock(mock_stock_data)
            
            # Test bearish breakout below lower band
            mock_indicators.return_value = {
                'current_price': 2630.0,  # Below lower band
                'bb_upper': 2930.0,
                'bb_lower': 2650.0, 
                'bb_middle': 2790.0,
                'bollinger_breakout': {
                    'upper_breakout': False,
                    'lower_breakout': True,
                    'breakout_strength': 0.8
                },
                'trend_signal': 'bearish',
                'momentum_signal': 'bearish', 
                'relative_volume': 2.3  # High volume confirms breakout
            }
            
            result_bearish_breakout = engine.analyze_stock(mock_stock_data)
            
            # Valid breakouts with volume confirmation should have high confidence
            # This indicates strong directional moves beyond normal trading range
            assert isinstance(result_bullish_breakout['confidence'], float)
            assert isinstance(result_bearish_breakout['confidence'], float)
            
            # After implementation: breakouts with volume should have higher confidence
            # Strong breakout setups are high-probability trading opportunities
    
    def test_bollinger_band_mean_reversion_vs_momentum(self, engine, mock_stock_data):
        """Test BB strategy adapts to market regime (mean reversion vs momentum)"""
        with patch.object(TechnicalIndicators, 'get_indicator_summary') as mock_indicators:
            # Mean reversion setup: price at extreme with no breakout momentum
            mock_indicators.return_value = {
                'current_price': 2950.0,  # Near upper band
                'bb_upper': 2940.0,
                'bb_lower': 2660.0,
                'bollinger_percent_b': 0.89,  # Overbought
                'bollinger_squeeze': {'is_squeeze': False},
                'bollinger_breakout': {'upper_breakout': False, 'breakout_strength': 0.1},
                'trend_signal': 'neutral',  # No strong trend
                'momentum_signal': 'neutral',
                'relative_volume': 0.8  # Low volume = weak signal
            }
            
            result_mean_reversion = engine.analyze_stock(mock_stock_data)
            
            # Momentum breakout setup: price breaking bands with strong signals
            mock_indicators.return_value = {
                'current_price': 2950.0,  # Above upper band  
                'bb_upper': 2940.0,
                'bb_lower': 2660.0,
                'bollinger_percent_b': 1.04,  # Beyond upper band
                'bollinger_squeeze': {'is_squeeze': False},
                'bollinger_breakout': {'upper_breakout': True, 'breakout_strength': 0.8},
                'trend_signal': 'bullish',  # Strong trend
                'momentum_signal': 'bullish', 
                'relative_volume': 2.5  # High volume = strong signal
            }
            
            result_momentum = engine.analyze_stock(mock_stock_data)
            
            # Decision engine should differentiate between:
            # 1. Mean reversion setup (fade the extreme) - lower confidence
            # 2. Momentum breakout setup (follow the move) - higher confidence  
            assert isinstance(result_mean_reversion['confidence'], float)
            assert isinstance(result_momentum['confidence'], float)
            
            # After implementation: momentum setup should have higher confidence
            # assert result_momentum['confidence'] > result_mean_reversion['confidence']


class TestRefactoredBollingerBandMethods:
    """Test refactored Bollinger Band scoring methods work correctly"""
    
    @pytest.fixture
    def engine(self):
        return DecisionEngine()
    
    def test_score_percent_b_signals_method(self, engine):
        """Test _score_percent_b_signals method works correctly"""
        # Test bullish overbought scenario
        score = engine._score_percent_b_signals(0.85, 'bullish')
        assert score == -0.1  # Should reduce confidence for overbought bullish
        
        # Test bullish healthy position
        score = engine._score_percent_b_signals(0.4, 'bullish')
        assert score == 0.05  # Should boost confidence for healthy bullish position
        
        # Test bearish oversold scenario
        score = engine._score_percent_b_signals(0.15, 'bearish')
        assert score == -0.1  # Should reduce confidence for oversold bearish (potential bounce)
        
        # Test bearish healthy position
        score = engine._score_percent_b_signals(0.6, 'bearish')
        assert score == 0.05  # Should boost confidence for healthy bearish position
        
        # Test neutral trend
        score = engine._score_percent_b_signals(0.5, 'neutral')
        assert score == 0.0  # Should not affect neutral trends
        
        # Test None input
        score = engine._score_percent_b_signals(None, 'bullish')
        assert score == 0.0  # Should handle None gracefully
    
    def test_score_squeeze_signals_method(self, engine):
        """Test _score_squeeze_signals method works correctly"""
        # Test active squeeze with trend alignment
        squeeze_info = {'is_squeeze': True, 'squeeze_intensity': 0.8, 'squeeze_periods': 10}
        score = engine._score_squeeze_signals(squeeze_info, 'bullish', 1.5)
        expected = 0.8 * 0.15 + 0.08  # intensity_boost + volume_boost
        assert score == expected
        
        # Test squeeze without volume confirmation
        score = engine._score_squeeze_signals(squeeze_info, 'bullish', 1.0)
        expected = 0.8 * 0.15  # only intensity_boost
        assert score == expected
        
        # Test no squeeze
        no_squeeze_info = {'is_squeeze': False, 'squeeze_intensity': 0.2, 'squeeze_periods': 2}
        score = engine._score_squeeze_signals(no_squeeze_info, 'bullish', 1.5)
        assert score == 0.0
        
        # Test neutral trend with squeeze
        score = engine._score_squeeze_signals(squeeze_info, 'neutral', 1.5)
        assert score == 0.0  # Should not boost neutral trends
        
        # Test empty squeeze info
        score = engine._score_squeeze_signals({}, 'bullish', 1.5)
        assert score == 0.0
    
    def test_score_breakout_signals_method(self, engine):
        """Test _score_breakout_signals method works correctly"""
        # Test aligned bullish breakout
        breakout_info = {'upper_breakout': True, 'lower_breakout': False, 'breakout_strength': 0.7}
        score = engine._score_breakout_signals(breakout_info, 'bullish')
        assert score == 0.7 * 0.2  # strength * multiplier
        
        # Test aligned bearish breakout
        breakout_info = {'upper_breakout': False, 'lower_breakout': True, 'breakout_strength': 0.6}
        score = engine._score_breakout_signals(breakout_info, 'bearish')
        assert score == 0.6 * 0.2
        
        # Test conflicting breakout (bullish trend with lower breakout)
        breakout_info = {'upper_breakout': False, 'lower_breakout': True, 'breakout_strength': 0.8}
        score = engine._score_breakout_signals(breakout_info, 'bullish')
        assert score == -0.12  # Should reduce confidence
        
        # Test no breakout
        breakout_info = {'upper_breakout': False, 'lower_breakout': False, 'breakout_strength': 0.1}
        score = engine._score_breakout_signals(breakout_info, 'bullish')
        assert score == 0.0
        
        # Test empty breakout info
        score = engine._score_breakout_signals({}, 'bullish')
        assert score == 0.0
    
    def test_score_market_regime_method(self, engine):
        """Test _score_market_regime method works correctly"""
        # Test mean reversion setup (extreme %B, weak breakout, low volume)
        breakout_info = {'breakout_strength': 0.2}
        score = engine._score_market_regime(0.9, breakout_info, 1.0)  # extreme %B, weak breakout, normal volume
        assert score == -0.08  # Should reduce confidence for mean reversion
        
        # Test momentum setup (moderate %B, strong breakout, high volume)
        breakout_info = {'breakout_strength': 0.7}
        score = engine._score_market_regime(0.5, breakout_info, 2.0)  # moderate %B, strong breakout, high volume
        assert score == 0.12  # Should boost confidence for momentum
        
        # Test normal conditions (no extreme setup)
        breakout_info = {'breakout_strength': 0.4}
        score = engine._score_market_regime(0.6, breakout_info, 1.3)
        assert score == 0.0  # Should not affect confidence
        
        # Test None inputs
        score = engine._score_market_regime(None, breakout_info, 1.5)
        assert score == 0.0
        
        score = engine._score_market_regime(0.5, {}, 1.5)
        assert score == 0.0
    
    def test_refactored_methods_integration(self, engine):
        """Test that refactored methods integrate correctly with main scoring"""
        # Test with comprehensive BB data
        indicators = {
            'trend_signal': 'bullish',
            'bollinger_percent_b': 0.4,  # Healthy bullish position
            'bollinger_squeeze': {'is_squeeze': True, 'squeeze_intensity': 0.6, 'squeeze_periods': 8},
            'bollinger_breakout': {'upper_breakout': True, 'lower_breakout': False, 'breakout_strength': 0.7},  # Strong breakout
            'relative_volume': 1.8
        }
        
        total_score = engine._score_bollinger_band_signals(indicators, 'bullish')
        
        # Should be sum of all individual scores
        percent_b_score = 0.05  # healthy bullish position
        squeeze_score = 0.6 * 0.15 + 0.08  # intensity + volume boost
        breakout_score = 0.7 * 0.2  # aligned breakout (updated for 0.7 strength)
        market_regime_score = 0.12  # momentum setup (moderate %B, strong breakout > 0.6, high volume)
        
        expected_total = percent_b_score + squeeze_score + breakout_score + market_regime_score
        assert abs(total_score - expected_total) < 0.001  # Allow for floating point precision


class TestErrorResilienceAndEdgeCases:
    """Test error resilience and edge case handling in BB integration"""
    
    @pytest.fixture  
    def engine(self):
        return DecisionEngine()
    
    @pytest.fixture
    def mock_stock_data_minimal(self):
        # Create minimal valid stock data
        data = pd.DataFrame({
            'Open': [100, 101, 102],
            'High': [102, 103, 104],
            'Low': [98, 99, 100],
            'Close': [100, 101, 102],
            'Volume': [1000, 1100, 1200],
            'Adj Close': [100, 101, 102]
        })
        return StockData(symbol='TEST.BO', data=data, period='3d')
    
    def test_decision_engine_handles_insufficient_bb_data(self, engine, mock_stock_data_minimal):
        """Test decision engine gracefully handles insufficient data for BB calculations"""
        result = engine.analyze_stock(mock_stock_data_minimal)
        
        # Should complete successfully without errors
        assert 'recommendation' in result
        assert 'action' in result['recommendation']
        assert 'confidence' in result
        assert isinstance(result['confidence'], float)
        assert 0.0 <= result['confidence'] <= 1.0
        
        # BB indicators should be present with safe defaults
        # (This tests that the new error handling works)
        assert result is not None
    
    def test_bb_scoring_with_malformed_data(self, engine):
        """Test BB scoring methods handle malformed/missing data gracefully"""
        # Test with missing/None BB indicators
        malformed_indicators = {
            'trend_signal': 'bullish',
            'bollinger_percent_b': None,
            'bollinger_squeeze': None,
            'bollinger_breakout': None,
            'relative_volume': None
        }
        
        # Should not crash and return reasonable default
        score = engine._score_bollinger_band_signals(malformed_indicators, 'bullish')
        assert isinstance(score, float)
        assert score == 0.0  # Should default to no adjustment
    
    def test_bb_scoring_with_incomplete_dict_data(self, engine):
        """Test BB scoring with incomplete dictionary data"""
        incomplete_indicators = {
            'trend_signal': 'bullish',
            'bollinger_percent_b': 0.5,
            'bollinger_squeeze': {'is_squeeze': True},  # Missing intensity/periods
            'bollinger_breakout': {'upper_breakout': True},  # Missing strength
            'relative_volume': 1.5
        }
        
        # Should handle missing keys gracefully
        score = engine._score_bollinger_band_signals(incomplete_indicators, 'bullish')
        assert isinstance(score, float)
        # Should work despite incomplete data
        assert score > 0  # Should still get some positive score from available data
    
    def test_individual_indicator_failures_dont_crash_summary(self):
        """Test that individual indicator failures don't crash the entire summary"""
        from src.analysis.indicators import TechnicalIndicators
        
        # Test with problematic data that might cause individual calculations to fail
        problematic_data = pd.DataFrame({
            'Close': [100.0, 100.0, 100.0, 100.0, 100.0, 100.0],  # Flat prices
            'Volume': [0, 0, 0, 0, 0, 0]  # Zero volume
        })
        
        indicators = TechnicalIndicators()
        result = indicators.get_indicator_summary(problematic_data)
        
        # Should complete and return a valid result
        assert isinstance(result, dict)
        assert 'current_price' in result
        assert 'bb_status' in result
        
        # Should have safe fallback values for BB indicators
        assert isinstance(result.get('bollinger_squeeze', {}), dict)
        assert isinstance(result.get('bollinger_breakout', {}), dict)
    
    def test_mixed_success_failure_scenario(self):
        """Test scenario where some indicators work and others fail"""
        from src.analysis.indicators import TechnicalIndicators
        
        # Create data where some indicators will work (sufficient length) 
        # but others might have issues (extreme values)
        mixed_data = pd.DataFrame({
            'Close': [1e-10, 1e10, 1e-5, 1e8, 100, 101] * 5,  # Extreme values that might cause issues
            'Volume': [1000, 1100, 1200, 1300, 1400, 1500] * 5
        })
        
        indicators = TechnicalIndicators()
        result = indicators.get_indicator_summary(mixed_data)
        
        # Should complete successfully
        assert isinstance(result, dict)
        assert 'bb_status' in result
        
        # Should report status appropriately
        bb_status = result.get('bb_status')
        assert bb_status is not None
        assert isinstance(bb_status, str)
        
        # Should have structured BB data even if calculations had issues
        assert isinstance(result.get('bollinger_squeeze', {}), dict)
        assert isinstance(result.get('bollinger_breakout', {}), dict)


class TestEnhancedRiskAssessment:
    """TDD tests for enhanced risk assessment with VaR, Sortino ratio, and improved beta"""
    
    @pytest.fixture
    def risk_assessor(self):
        return RiskAssessment()
    
    @pytest.fixture
    def mock_stock_data(self, sample_stock_data):
        return StockData(
            symbol="RELIANCE.BO", 
            data=sample_stock_data, 
            period="1y"
        )
    
    @pytest.fixture
    def mock_market_data(self, sample_stock_data):
        """Mock market index data (e.g., Nifty 50)"""
        # Create correlated market data
        np.random.seed(42)  # For reproducible tests
        market_returns = sample_stock_data['Close'].pct_change().dropna() * 0.8 + np.random.normal(0, 0.01, len(sample_stock_data)-1)
        market_prices = [1000]  # Starting price
        for ret in market_returns:
            market_prices.append(market_prices[-1] * (1 + ret))
        
        market_df = pd.DataFrame({
            'Close': market_prices,
            'Volume': [10000000] * len(market_prices),
            'Open': [p * 0.995 for p in market_prices],
            'High': [p * 1.005 for p in market_prices],
            'Low': [p * 0.995 for p in market_prices],
            'Adj Close': market_prices
        }, index=sample_stock_data.index)
        
        return StockData(symbol="^NSEI", data=market_df, period="1y")
    
    # Value at Risk (VaR) Tests
    
    def test_calculate_var_95_percent_confidence(self, risk_assessor, mock_stock_data):
        """Test VaR calculation at 95% confidence level"""
        var_95 = risk_assessor.calculate_var(mock_stock_data, confidence_level=0.95)
        
        assert isinstance(var_95, dict)
        assert 'var_amount' in var_95
        assert 'var_percentage' in var_95
        assert 'confidence_level' in var_95
        
        # VaR should be negative (representing potential loss)
        assert var_95['var_percentage'] < 0
        assert var_95['var_amount'] < 0
        assert var_95['confidence_level'] == 0.95
        
        # VaR amount should be in rupees
        current_price = mock_stock_data.current_price
        expected_var_amount = current_price * var_95['var_percentage']
        assert abs(var_95['var_amount'] - expected_var_amount) < 0.01
    
    def test_calculate_var_99_percent_confidence(self, risk_assessor, mock_stock_data):
        """Test VaR calculation at 99% confidence level"""
        var_99 = risk_assessor.calculate_var(mock_stock_data, confidence_level=0.99)
        
        assert isinstance(var_99, dict)
        assert var_99['confidence_level'] == 0.99
        
        # 99% VaR should be larger (more negative) than 95% VaR
        var_95 = risk_assessor.calculate_var(mock_stock_data, confidence_level=0.95)
        assert var_99['var_percentage'] < var_95['var_percentage']  # More negative
    
    def test_calculate_var_different_time_horizons(self, risk_assessor, mock_stock_data):
        """Test VaR calculation for different time horizons"""
        var_1_day = risk_assessor.calculate_var(mock_stock_data, confidence_level=0.95, time_horizon=1)
        var_10_day = risk_assessor.calculate_var(mock_stock_data, confidence_level=0.95, time_horizon=10)
        
        # 10-day VaR should be larger than 1-day VaR (scaled by sqrt of time)
        expected_scaling = np.sqrt(10)
        expected_10_day = var_1_day['var_percentage'] * expected_scaling
        
        assert abs(var_10_day['var_percentage'] - expected_10_day) < 0.05  # Allow some tolerance
    
    def test_calculate_var_insufficient_data(self, risk_assessor):
        """Test VaR calculation with insufficient data"""
        # Create minimal dataset
        minimal_data = pd.DataFrame({
            'Close': [100, 101, 99],  # Only 3 data points
            'Volume': [1000, 1100, 900],
            'Open': [99, 100, 101],
            'High': [102, 103, 100],
            'Low': [98, 99, 97],
            'Adj Close': [100, 101, 99]
        })
        minimal_stock_data = StockData(symbol="TEST.BO", data=minimal_data, period="3d")
        
        var_result = risk_assessor.calculate_var(minimal_stock_data)
        
        # Should handle gracefully, possibly return None or use available data
        assert var_result is not None
        assert isinstance(var_result, dict)
    
    # Sortino Ratio Tests
    
    def test_calculate_sortino_ratio(self, risk_assessor, mock_stock_data):
        """Test Sortino ratio calculation (downside deviation version of Sharpe)"""
        sortino_ratio = risk_assessor.calculate_sortino_ratio(mock_stock_data, risk_free_rate=0.05)
        
        assert isinstance(sortino_ratio, float)
        assert not pd.isna(sortino_ratio)
        
        # Sortino ratio should be a reasonable value
        assert -5 <= sortino_ratio <= 10  # Reasonable bounds for Sortino ratio
    
    def test_sortino_vs_sharpe_ratio(self, risk_assessor, mock_stock_data):
        """Test that Sortino ratio differs from Sharpe ratio appropriately"""
        sortino_ratio = risk_assessor.calculate_sortino_ratio(mock_stock_data, risk_free_rate=0.05)
        
        # Calculate existing Sharpe ratio for comparison
        risk_metrics = risk_assessor.assess_risk(mock_stock_data)
        sharpe_ratio = risk_metrics.get('sharpe_ratio', 0)
        
        # Both should be calculated
        assert sortino_ratio is not None
        assert sharpe_ratio is not None
        
        # They should be different (unless returns are perfectly symmetric)
        # For most real stocks, Sortino should be higher than Sharpe
        assert sortino_ratio != sharpe_ratio
    
    
    # Enhanced Beta Calculation Tests
    
    def test_calculate_enhanced_beta_with_market_data(self, risk_assessor, mock_stock_data, mock_market_data):
        """Test enhanced beta calculation with actual market data"""
        beta = risk_assessor.calculate_enhanced_beta(mock_stock_data, mock_market_data)
        
        assert isinstance(beta, dict)
        assert 'beta' in beta
        assert 'correlation' in beta
        assert 'r_squared' in beta
        assert 'alpha' in beta
        
        # Beta should be reasonable
        assert 0 <= beta['beta'] <= 5  # Most stocks have beta between 0-3
        
        # Correlation should be between -1 and 1
        assert -1 <= beta['correlation'] <= 1
        
        # R-squared should be between 0 and 1
        assert 0 <= beta['r_squared'] <= 1
        
        # Alpha can be positive or negative
        assert isinstance(beta['alpha'], (int, float))
    
    def test_calculate_enhanced_beta_different_time_periods(self, risk_assessor, mock_stock_data, mock_market_data):
        """Test beta calculation over different time periods"""
        # Calculate beta for full period
        beta_full = risk_assessor.calculate_enhanced_beta(mock_stock_data, mock_market_data)
        
        # Calculate beta for recent period (last 6 months)
        recent_data = StockData(
            symbol=mock_stock_data.symbol,
            data=mock_stock_data.data.tail(126),  # Approx 6 months
            period="6m"
        )
        recent_market = StockData(
            symbol=mock_market_data.symbol,
            data=mock_market_data.data.tail(126),
            period="6m"
        )
        
        beta_recent = risk_assessor.calculate_enhanced_beta(recent_data, recent_market)
        
        # Both should be valid calculations
        assert beta_full['beta'] is not None
        assert beta_recent['beta'] is not None
        
        # They may be different due to changing market conditions
        # This test ensures both time periods are handled
    
    def test_calculate_enhanced_beta_insufficient_data(self, risk_assessor):
        """Test beta calculation with insufficient data"""
        # Create minimal datasets
        minimal_data = pd.DataFrame({
            'Close': [100, 102, 99],
            'Volume': [1000, 1100, 900],
            'Open': [99, 100, 101], 'High': [102, 103, 100], 
            'Low': [98, 99, 97], 'Adj Close': [100, 102, 99]
        })
        
        minimal_market = pd.DataFrame({
            'Close': [1000, 1010, 995],
            'Volume': [10000, 11000, 9500],
            'Open': [999, 1000, 1001], 'High': [1020, 1030, 1000],
            'Low': [980, 990, 970], 'Adj Close': [1000, 1010, 995]
        })
        
        stock_data = StockData(symbol="TEST.BO", data=minimal_data, period="3d")
        market_data = StockData(symbol="^NSEI", data=minimal_market, period="3d")
        
        beta_result = risk_assessor.calculate_enhanced_beta(stock_data, market_data)
        
        # Should handle gracefully
        assert beta_result is not None
        assert isinstance(beta_result, dict)
        # May return default beta of 1.0 or None for insufficient data
    
    # Rolling Risk Metrics Tests
    
    def test_calculate_rolling_volatility(self, risk_assessor, mock_stock_data):
        """Test calculation of rolling volatility over different windows"""
        rolling_vol = risk_assessor.calculate_rolling_volatility(mock_stock_data, window=30)
        
        assert isinstance(rolling_vol, dict)
        assert 'current_volatility' in rolling_vol
        assert 'average_volatility' in rolling_vol
        assert 'volatility_trend' in rolling_vol
        assert 'volatility_series' in rolling_vol
        
        # Current volatility should be positive
        assert rolling_vol['current_volatility'] > 0
        assert rolling_vol['average_volatility'] > 0
        
        # Volatility trend should be 'increasing', 'decreasing', or 'stable'
        assert rolling_vol['volatility_trend'] in ['increasing', 'decreasing', 'stable']
        
        # Volatility series should be a pandas Series
        assert isinstance(rolling_vol['volatility_series'], pd.Series)
    
    def test_calculate_rolling_drawdown(self, risk_assessor, mock_stock_data):
        """Test calculation of rolling drawdown"""
        rolling_dd = risk_assessor.calculate_rolling_drawdown(mock_stock_data, window=60)
        
        assert isinstance(rolling_dd, dict)
        assert 'current_drawdown' in rolling_dd
        assert 'max_drawdown' in rolling_dd
        assert 'average_drawdown' in rolling_dd
        assert 'drawdown_duration' in rolling_dd
        
        # Drawdowns should be negative or zero
        assert rolling_dd['current_drawdown'] <= 0
        assert rolling_dd['max_drawdown'] <= 0
        assert rolling_dd['average_drawdown'] <= 0
        
        # Duration should be non-negative integer
        assert isinstance(rolling_dd['drawdown_duration'], int)
        assert rolling_dd['drawdown_duration'] >= 0
    
    # Comprehensive Enhanced Risk Assessment
    
    def test_enhanced_risk_assessment_integration(self, risk_assessor, mock_stock_data, mock_market_data):
        """Test complete enhanced risk assessment"""
        enhanced_risk = risk_assessor.assess_enhanced_risk(mock_stock_data, mock_market_data)
        
        assert isinstance(enhanced_risk, dict)
        
        # Should include all traditional metrics
        traditional_metrics = ['volatility', 'max_drawdown', 'sharpe_ratio', 'daily_volatility']
        for metric in traditional_metrics:
            assert metric in enhanced_risk
        
        # Should include new enhanced metrics
        enhanced_metrics = ['var_95', 'sortino_ratio', 'enhanced_beta', 'rolling_volatility']
        for metric in enhanced_metrics:
            assert metric in enhanced_risk
        
        # VaR should be structured dict
        assert isinstance(enhanced_risk['var_95'], dict)
        assert 'var_percentage' in enhanced_risk['var_95']
        
        # Enhanced beta should be structured dict
        assert isinstance(enhanced_risk['enhanced_beta'], dict)
        assert 'beta' in enhanced_risk['enhanced_beta']
        
        # Should still have risk level classification
        assert 'risk_level' in enhanced_risk
        assert enhanced_risk['risk_level'] in ['LOW', 'MEDIUM', 'HIGH']
    
    def test_enhanced_risk_assessment_without_market_data(self, risk_assessor, mock_stock_data):
        """Test enhanced risk assessment when market data unavailable"""
        enhanced_risk = risk_assessor.assess_enhanced_risk(mock_stock_data, market_data=None)
        
        # Should still calculate most metrics
        assert isinstance(enhanced_risk, dict)
        assert 'var_95' in enhanced_risk
        assert 'sortino_ratio' in enhanced_risk
        assert 'rolling_volatility' in enhanced_risk
        
        # Beta-related metrics should be None or default values
        assert enhanced_risk.get('enhanced_beta') is None or enhanced_risk['enhanced_beta']['beta'] == 1.0
    
    # Risk Level Classification Enhancement
    
    def test_enhanced_risk_classification(self, risk_assessor):
        """Test enhanced risk level classification using multiple metrics"""
        # Mock comprehensive risk metrics
        comprehensive_metrics = {
            'daily_volatility': 0.025,
            'max_drawdown': -0.15,
            'var_95': {'var_percentage': -0.08},
            'sortino_ratio': 0.65,
            'enhanced_beta': {'beta': 1.2, 'correlation': 0.75},
            'rolling_volatility': {'volatility_trend': 'stable'}
        }
        
        risk_level = risk_assessor.classify_enhanced_risk_level(comprehensive_metrics)
        
        assert risk_level in ['LOW', 'MEDIUM', 'HIGH']
        # This classification should be more nuanced than simple vol + drawdown
    
    def test_risk_classification_edge_cases(self, risk_assessor):
        """Test risk classification with edge case metrics"""
        # Very high risk metrics
        high_risk_metrics = {
            'daily_volatility': 0.08,  # 8% daily vol
            'max_drawdown': -0.45,     # 45% max drawdown
            'var_95': {'var_percentage': -0.25},  # 25% VaR
            'sortino_ratio': -0.5,     # Negative Sortino
            'enhanced_beta': {'beta': 2.5, 'correlation': 0.9},
        }
        
        high_risk_level = risk_assessor.classify_enhanced_risk_level(high_risk_metrics)
        assert high_risk_level == 'HIGH'
        
        # Very low risk metrics
        low_risk_metrics = {
            'daily_volatility': 0.005,  # 0.5% daily vol
            'max_drawdown': -0.03,      # 3% max drawdown
            'var_95': {'var_percentage': -0.02},  # 2% VaR
            'sortino_ratio': 2.5,       # Excellent Sortino
            'enhanced_beta': {'beta': 0.3, 'correlation': 0.4},
        }
        
        low_risk_level = risk_assessor.classify_enhanced_risk_level(low_risk_metrics)
        assert low_risk_level == 'LOW'
    
    # Error Handling and Edge Cases
    
    def test_risk_assessment_with_flat_prices(self, risk_assessor):
        """Test risk assessment with flat/unchanging prices"""
        # Create flat price data
        flat_data = pd.DataFrame({
            'Close': [1000] * 100,  # Flat prices
            'Volume': [5000] * 100,
            'Open': [1000] * 100, 'High': [1000] * 100, 
            'Low': [1000] * 100, 'Adj Close': [1000] * 100
        })
        
        flat_stock_data = StockData(symbol="FLAT.BO", data=flat_data, period="100d")
        
        enhanced_risk = risk_assessor.assess_enhanced_risk(flat_stock_data)
        
        # Should handle zero volatility gracefully
        assert enhanced_risk['volatility'] == 0.0 or enhanced_risk['volatility'] is None
        assert enhanced_risk['max_drawdown'] == 0.0
        
        # VaR should be zero for no price movement
        assert enhanced_risk['var_95']['var_percentage'] == 0.0
        
        # Sharpe and Sortino ratios should be zero or None
        assert enhanced_risk['sharpe_ratio'] == 0.0 or enhanced_risk['sharpe_ratio'] is None
    
    def test_risk_assessment_with_extreme_outliers(self, risk_assessor):
        """Test risk assessment handles extreme price outliers"""
        # Create data with extreme outliers
        normal_prices = [1000 + i for i in range(90)]  # Normal progression
        outlier_prices = normal_prices + [10000, 100, 5000, 500, 2000]  # Add extreme outliers
        
        outlier_data = pd.DataFrame({
            'Close': outlier_prices,
            'Volume': [5000] * len(outlier_prices),
            'Open': outlier_prices, 'High': [p * 1.01 for p in outlier_prices],
            'Low': [p * 0.99 for p in outlier_prices], 'Adj Close': outlier_prices
        })
        
        outlier_stock_data = StockData(symbol="OUTLIER.BO", data=outlier_data, period="95d")
        
        enhanced_risk = risk_assessor.assess_enhanced_risk(outlier_stock_data)
        
        # Should still produce reasonable risk metrics
        assert enhanced_risk is not None
        assert enhanced_risk['risk_level'] in ['LOW', 'MEDIUM', 'HIGH']
        # Likely HIGH due to extreme volatility from outliers
        
        # All metrics should be calculable (not None or NaN)
        assert not pd.isna(enhanced_risk.get('volatility', 0))
        assert not pd.isna(enhanced_risk.get('max_drawdown', 0))