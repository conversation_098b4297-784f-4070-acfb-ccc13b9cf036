import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from src.analysis.fundamental import FundamentalAnalysis
from src.data.yahoo_client import StockData

# A very small number
epsilon = 1e-9 

class TestFundamentalAnalysis:
    """Test fundamental analysis calculations using TDD approach"""
    
    @pytest.fixture
    def fundamental_analyzer(self):
        return FundamentalAnalysis()
    
    @pytest.fixture
    def mock_stock_data(self, sample_stock_data):
        return StockData(
            symbol="RELIANCE.BO",
            data=sample_stock_data,
            period="1y"
        )
    
    @pytest.fixture
    def mock_ticker_info(self):
        """Mock ticker.info data from yfinance with Indian stock fundamentals"""
        return {
            # Valuation metrics
            'forwardPE': 24.5,
            'trailingPE': 22.8,
            'priceToBook': 2.1,
            'enterpriseToRevenue': 3.2,
            'enterpriseToEbitda': 12.5,
            
            # Financial metrics
            'returnOnEquity': 0.128,  # 12.8%
            'returnOnAssets': 0.065,  # 6.5%
            'debtToEquity': 0.35,     # 35%
            'currentRatio': 1.45,
            'quickRatio': 0.95,
            
            # Growth metrics
            'revenueGrowth': 0.085,   # 8.5%
            'earningsGrowth': 0.12,   # 12%
            'quarterlyRevenueGrowth': 0.06,  # 6% QoQ
            'quarterlyEarningsGrowth': 0.15, # 15% QoQ
            
            # Dividend metrics
            'dividendYield': 0.008,   # 0.8%
            'dividendRate': 8.0,      # ₹8 per share
            'payoutRatio': 0.25,      # 25%
            
            # Market data
            'marketCap': 1800000000000,  # ₹18 trillion
            'enterpriseValue': 1900000000000,
            'sharesOutstanding': 6770000000,
            
            # Company info
            'longName': 'Reliance Industries Limited',
            'sector': 'Energy',
            'industry': 'Oil & Gas Integrated',
            'country': 'India',
            'currency': 'INR',
            
            # Financial statement data
            'totalRevenue': ************,  # ₹792 billion
            'totalDebt': ************,     # ₹350 billion
            'totalCash': ************,     # ₹280 billion
            'bookValue': 1250.0,           # ₹1,250 per share
        }
    
    def test_fundamental_analyzer_initialization(self, fundamental_analyzer):
        """Test that FundamentalAnalysis initializes properly"""
        assert fundamental_analyzer is not None
        assert hasattr(fundamental_analyzer, 'calculate_pe_ratio')
        assert hasattr(fundamental_analyzer, 'calculate_pb_ratio')
        assert hasattr(fundamental_analyzer, 'calculate_debt_to_equity')
        assert hasattr(fundamental_analyzer, 'calculate_roe')
        assert hasattr(fundamental_analyzer, 'calculate_roa')
        assert hasattr(fundamental_analyzer, 'calculate_dividend_yield')
        assert hasattr(fundamental_analyzer, 'get_fundamental_summary')
    
    # Valuation Metrics Tests
    
    def test_calculate_pe_ratio_from_ticker_data(self, fundamental_analyzer, mock_ticker_info):
        """Test P/E ratio calculation from ticker info"""
        pe_ratio = fundamental_analyzer.calculate_pe_ratio(mock_ticker_info)
        
        # Should return trailing P/E if available
        assert abs(pe_ratio - 22.8) < epsilon
        assert isinstance(pe_ratio, (int, float))
        assert pe_ratio > 0
    
    def test_calculate_pe_ratio_forward_fallback(self, fundamental_analyzer):
        """Test P/E ratio fallback to forward P/E when trailing not available"""
        ticker_info = {'forwardPE': 25.5}  # Only forward P/E available
        
        pe_ratio = fundamental_analyzer.calculate_pe_ratio(ticker_info)
        assert abs(pe_ratio - 25.5) < epsilonpe_ratio
    
    def test_calculate_pe_ratio_manual_calculation(self, fundamental_analyzer, mock_stock_data):
        """Test manual P/E calculation when ticker data unavailable"""
        ticker_info = {}  # No P/E data in ticker info
        current_price = 2650.0
        earnings_per_share = 116.5  # Mock EPS
        
        pe_ratio = fundamental_analyzer.calculate_pe_ratio(ticker_info, current_price, earnings_per_share)
        expected_pe = current_price / earnings_per_share
        
        assert abs(pe_ratio - expected_pe) < 0.01
        assert pe_ratio > 0
    
    def test_calculate_pe_ratio_invalid_data_handling(self, fundamental_analyzer):
        """Test P/E ratio calculation with invalid/missing data"""
        # Test with None values
        pe_ratio = fundamental_analyzer.calculate_pe_ratio({})
        assert pe_ratio is None
        
        # Test with zero EPS
        pe_ratio = fundamental_analyzer.calculate_pe_ratio({}, 100.0, 0.0)
        assert pe_ratio is None
        
        # Test with negative EPS
        pe_ratio = fundamental_analyzer.calculate_pe_ratio({}, 100.0, -10.0)
        assert pe_ratio is None
    
    def test_calculate_pb_ratio_from_ticker_data(self, fundamental_analyzer, mock_ticker_info):
        """Test P/B ratio calculation from ticker info"""
        pb_ratio = fundamental_analyzer.calculate_pb_ratio(mock_ticker_info)
        
        assert pb_ratio == 2.1
        assert isinstance(pb_ratio, (int, float))
        assert pb_ratio > 0
    
    def test_calculate_pb_ratio_manual_calculation(self, fundamental_analyzer):
        """Test manual P/B calculation when ticker data unavailable"""
        ticker_info = {}  # No P/B data
        current_price = 2650.0
        book_value_per_share = 1250.0
        
        pb_ratio = fundamental_analyzer.calculate_pb_ratio(ticker_info, current_price, book_value_per_share)
        expected_pb = current_price / book_value_per_share
        
        assert abs(pb_ratio - expected_pb) < 0.01
        assert pb_ratio > 0
    
    # Profitability Metrics Tests
    
    def test_calculate_roe(self, fundamental_analyzer, mock_ticker_info):
        """Test ROE calculation from ticker info"""
        roe = fundamental_analyzer.calculate_roe(mock_ticker_info)
        
        assert roe == 12.8  # Should be converted to percentage
        assert isinstance(roe, (int, float))
        assert 0 <= roe <= 100  # ROE should be reasonable percentage
    
    def test_calculate_roe_manual_calculation(self, fundamental_analyzer):
        """Test manual ROE calculation"""
        ticker_info = {}  # No ROE data
        net_income = 50000000000  # ₹50 billion
        shareholders_equity = 400000000000  # ₹400 billion
        
        roe = fundamental_analyzer.calculate_roe(ticker_info, net_income, shareholders_equity)
        expected_roe = (net_income / shareholders_equity) * 100
        
        assert abs(roe - expected_roe) < 0.01
        assert roe > 0
    
    def test_calculate_roa(self, fundamental_analyzer, mock_ticker_info):
        """Test ROA calculation from ticker info"""
        roa = fundamental_analyzer.calculate_roa(mock_ticker_info)
        
        assert roa == 6.5  # Should be converted to percentage
        assert isinstance(roa, (int, float))
        assert 0 <= roa <= 50  # ROA should be reasonable percentage
    
    def test_calculate_debt_to_equity(self, fundamental_analyzer, mock_ticker_info):
        """Test Debt-to-Equity ratio calculation"""
        debt_to_equity = fundamental_analyzer.calculate_debt_to_equity(mock_ticker_info)
        
        assert debt_to_equity == 0.35
        assert isinstance(debt_to_equity, (int, float))
        assert debt_to_equity >= 0  # D/E ratio should be non-negative
    
    def test_calculate_debt_to_equity_manual_calculation(self, fundamental_analyzer):
        """Test manual D/E calculation"""
        ticker_info = {}  # No D/E data
        total_debt = ************  # ₹350 billion
        shareholders_equity = 1000000000000  # ₹1 trillion
        
        debt_to_equity = fundamental_analyzer.calculate_debt_to_equity(ticker_info, total_debt, shareholders_equity)
        expected_de = total_debt / shareholders_equity
        
        assert abs(debt_to_equity - expected_de) < 0.01
        assert debt_to_equity >= 0
    
    # Growth Metrics Tests
    
    def test_calculate_revenue_growth(self, fundamental_analyzer, mock_ticker_info):
        """Test revenue growth calculation"""
        yoy_growth, qoq_growth = fundamental_analyzer.calculate_revenue_growth(mock_ticker_info)
        
        assert yoy_growth == 8.5  # Should be converted to percentage
        assert qoq_growth == 6.0
        assert isinstance(yoy_growth, (int, float))
        assert isinstance(qoq_growth, (int, float))
    
    def test_calculate_earnings_growth(self, fundamental_analyzer, mock_ticker_info):
        """Test earnings growth calculation"""
        yoy_growth, qoq_growth = fundamental_analyzer.calculate_earnings_growth(mock_ticker_info)
        
        assert yoy_growth == 12.0  # Should be converted to percentage
        assert qoq_growth == 15.0
        assert isinstance(yoy_growth, (int, float))
        assert isinstance(qoq_growth, (int, float))
    
    def test_calculate_growth_with_historical_data(self, fundamental_analyzer):
        """Test growth calculation using historical financial data"""
        # Mock quarterly revenue data (most recent first)
        quarterly_revenues = [200000000000, 190000000000, 185000000000, 175000000000]  # Q1, Q2, Q3, Q4
        
        qoq_growth = fundamental_analyzer.calculate_qoq_growth(quarterly_revenues)
        yoy_growth = fundamental_analyzer.calculate_yoy_growth(quarterly_revenues, quarters_back=3)
        
        # QoQ: (200B - 190B) / 190B * 100 = 5.26%
        expected_qoq = ((quarterly_revenues[0] - quarterly_revenues[1]) / quarterly_revenues[1]) * 100
        assert abs(qoq_growth - expected_qoq) < 0.01
        
        # YoY: (200B - 175B) / 175B * 100 = 14.29%
        expected_yoy = ((quarterly_revenues[0] - quarterly_revenues[3]) / quarterly_revenues[3]) * 100
        assert abs(yoy_growth - expected_yoy) < 0.01
    
    # Dividend Metrics Tests
    
    def test_calculate_dividend_yield(self, fundamental_analyzer, mock_ticker_info):
        """Test dividend yield calculation"""
        dividend_yield = fundamental_analyzer.calculate_dividend_yield(mock_ticker_info)
        
        assert dividend_yield == 0.8  # Should be converted to percentage
        assert isinstance(dividend_yield, (int, float))
        assert 0 <= dividend_yield <= 15  # Reasonable dividend yield range
    
    def test_calculate_dividend_yield_manual_calculation(self, fundamental_analyzer):
        """Test manual dividend yield calculation"""
        ticker_info = {}  # No dividend data
        annual_dividend = 8.0  # ₹8 per share
        current_price = 2650.0
        
        dividend_yield = fundamental_analyzer.calculate_dividend_yield(ticker_info, annual_dividend, current_price)
        expected_yield = (annual_dividend / current_price) * 100
        
        assert abs(dividend_yield - expected_yield) < 0.01
        assert dividend_yield >= 0
    
    # Comprehensive Analysis Tests
    
    def test_get_fundamental_summary(self, fundamental_analyzer, mock_stock_data, mock_ticker_info):
        """Test comprehensive fundamental analysis summary"""
        with patch('yfinance.Ticker') as mock_ticker_class:
            mock_ticker = Mock()
            mock_ticker.info = mock_ticker_info
            mock_ticker_class.return_value = mock_ticker
            
            summary = fundamental_analyzer.get_fundamental_summary(mock_stock_data)
            
            # Check that all fundamental metrics are present
            assert isinstance(summary, dict)
            
            # Valuation metrics
            assert 'pe_ratio' in summary
            assert 'pb_ratio' in summary
            assert summary['pe_ratio'] == 22.8
            assert summary['pb_ratio'] == 2.1
            
            # Profitability metrics
            assert 'roe' in summary
            assert 'roa' in summary
            assert 'debt_to_equity' in summary
            assert summary['roe'] == 12.8
            assert summary['roa'] == 6.5
            assert summary['debt_to_equity'] == 0.35
            
            # Growth metrics
            assert 'revenue_growth_yoy' in summary
            assert 'revenue_growth_qoq' in summary
            assert 'earnings_growth_yoy' in summary
            assert 'earnings_growth_qoq' in summary
            
            # Dividend metrics
            assert 'dividend_yield' in summary
            assert summary['dividend_yield'] == 0.8
            
            # Company info
            assert 'company_name' in summary
            assert 'sector' in summary
            assert 'industry' in summary
            assert summary['company_name'] == 'Reliance Industries Limited'
    
    def test_fundamental_summary_with_missing_data(self, fundamental_analyzer, mock_stock_data):
        """Test fundamental summary handles missing ticker data gracefully"""
        with patch('yfinance.Ticker') as mock_ticker_class:
            mock_ticker = Mock()
            mock_ticker.info = {}  # Empty ticker info
            mock_ticker_class.return_value = mock_ticker
            
            summary = fundamental_analyzer.get_fundamental_summary(mock_stock_data)
            
            # Should still return a dictionary with None values for missing data
            assert isinstance(summary, dict)
            assert 'pe_ratio' in summary
            assert 'pb_ratio' in summary
            assert 'roe' in summary
            
            # Missing data should be None
            assert summary['pe_ratio'] is None
            assert summary['pb_ratio'] is None
            assert summary['roe'] is None
    
    # Sector Comparison Tests
    
    def test_compare_with_sector_averages(self, fundamental_analyzer):
        """Test sector comparison functionality"""
        company_metrics = {
            'pe_ratio': 22.8,
            'pb_ratio': 2.1,
            'roe': 12.8,
            'debt_to_equity': 0.35
        }
        
        # Mock sector averages for Energy sector
        sector_averages = {
            'pe_ratio': 18.5,
            'pb_ratio': 1.8,
            'roe': 10.2,
            'debt_to_equity': 0.45
        }
        
        comparison = fundamental_analyzer.compare_with_sector(company_metrics, sector_averages)
        
        assert isinstance(comparison, dict)
        assert 'pe_vs_sector' in comparison
        assert 'pb_vs_sector' in comparison
        assert 'roe_vs_sector' in comparison
        
        # P/E ratio is higher than sector average
        assert comparison['pe_vs_sector'] > 0
        # ROE is better than sector average
        assert comparison['roe_vs_sector'] > 0
    
    # Edge Cases and Error Handling Tests
    
    def test_handle_zero_denominators(self, fundamental_analyzer):
        """Test handling of zero denominators in calculations"""
        # P/E with zero EPS
        pe_ratio = fundamental_analyzer.calculate_pe_ratio({}, 100.0, 0.0)
        assert pe_ratio is None
        
        # P/B with zero book value
        pb_ratio = fundamental_analyzer.calculate_pb_ratio({}, 100.0, 0.0)
        assert pb_ratio is None
        
        # ROE with zero equity
        roe = fundamental_analyzer.calculate_roe({}, 50000, 0)
        assert roe is None
        
        # D/E with zero equity
        de_ratio = fundamental_analyzer.calculate_debt_to_equity({}, 100000, 0)
        assert de_ratio is None
    
    def test_handle_negative_values(self, fundamental_analyzer):
        """Test handling of negative values in financial metrics"""
        # Negative earnings should result in None P/E
        pe_ratio = fundamental_analyzer.calculate_pe_ratio({}, 100.0, -10.0)
        assert pe_ratio is None
        
        # Negative equity should result in None ROE
        roe = fundamental_analyzer.calculate_roe({}, 50000, -100000)
        assert roe is None
        
        # Negative book value should result in None P/B
        pb_ratio = fundamental_analyzer.calculate_pb_ratio({}, 100.0, -50.0)
        assert pb_ratio is None
    
    def test_handle_extreme_values(self, fundamental_analyzer):
        """Test handling of extreme values"""
        # Very high P/E ratio (should be capped or flagged)
        pe_ratio = fundamental_analyzer.calculate_pe_ratio({}, 1000.0, 0.1)
        assert pe_ratio == pytest.approx(10000.0) or pe_ratio is None  # Implementation should decide
        
        # Very high debt-to-equity ratio
        de_ratio = fundamental_analyzer.calculate_debt_to_equity({}, 1000000000, 1000)
        assert de_ratio == pytest.approx(1000000.0) or de_ratio is None  # Implementation should decide
    
    # Data Validation Tests
    
    def test_validate_ticker_info_structure(self, fundamental_analyzer):
        """Test validation of ticker info data structure"""
        # Valid ticker info
        valid_ticker_info = {'forwardPE': 25.0, 'returnOnEquity': 0.15}
        assert fundamental_analyzer._validate_ticker_info(valid_ticker_info) == True
        
        # Invalid ticker info (None)
        assert fundamental_analyzer._validate_ticker_info(None) == False
        
        # Invalid ticker info (not dict)
        assert fundamental_analyzer._validate_ticker_info("invalid") == False
        
        # Empty ticker info (should be valid)
        assert fundamental_analyzer._validate_ticker_info({}) == True
    
    def test_financial_ratios_reasonableness_check(self, fundamental_analyzer):
        """Test reasonableness checks for calculated ratios"""
        # Test that calculated ratios are within reasonable bounds
        ratios = {
            'pe_ratio': 25.5,
            'pb_ratio': 2.1,
            'roe': 15.8,
            'roa': 8.2,
            'debt_to_equity': 0.65,
            'dividend_yield': 2.5
        }
        
        reasonableness_check = fundamental_analyzer.check_ratio_reasonableness(ratios)
        
        assert isinstance(reasonableness_check, dict)
        assert 'flags' in reasonableness_check
        assert 'warnings' in reasonableness_check
        
        # All these ratios should be reasonable
        assert len(reasonableness_check['flags']) == 0  # No red flags
        
        # Test unreasonable ratios
        unreasonable_ratios = {
            'pe_ratio': 500.0,  # Very high P/E
            'pb_ratio': 50.0,   # Very high P/B
            'roe': 150.0,       # Impossibly high ROE
            'debt_to_equity': 10.0  # Very high debt
        }
        
        unreasonable_check = fundamental_analyzer.check_ratio_reasonableness(unreasonable_ratios)
        assert len(unreasonable_check['flags']) > 0  # Should have red flags

class TestFundamentalAnalysisIntegration:
    """Test integration of fundamental analysis with other components"""
    
    @pytest.fixture
    def fundamental_analyzer(self):
        return FundamentalAnalysis()
    
    def test_integration_with_yahoo_client(self, fundamental_analyzer):
        """Test that fundamental analysis integrates properly with Yahoo Finance client"""
        from src.data.yahoo_client import YahooClient
        
        # This test will be implemented once YahooClient is extended
        # for now, we define the expected interface
        
        yahoo_client = YahooClient()
        # Expected: yahoo_client.fetch_fundamental_data(symbol) -> dict
        
        # Test that fundamental analyzer can work with YahooClient data
        assert hasattr(yahoo_client, 'fetch_stock_data')
        # Future: assert hasattr(yahoo_client, 'fetch_fundamental_data')
    
    def test_integration_with_decision_engine(self, fundamental_analyzer, sample_stock_data):
        """Test that fundamental metrics can be used by decision engine"""
        # Test that fundamental summary provides data in format expected by decision engine
        
        with patch('yfinance.Ticker') as mock_ticker_class:
            mock_ticker = Mock()
            mock_ticker.info = {
                'forwardPE': 25.0,
                'returnOnEquity': 0.15,
                'dividendYield': 0.02
            }
            mock_ticker_class.return_value = mock_ticker
            
            stock_data = StockData("TEST.BO", sample_stock_data, "1y")
            fundamental_summary = fundamental_analyzer.get_fundamental_summary(stock_data)
            
            # Check that summary contains keys expected by decision engine
            required_keys = ['pe_ratio', 'pb_ratio', 'roe', 'roa', 'debt_to_equity', 'dividend_yield']
            for key in required_keys:
                assert key in fundamental_summary
                # Values should be numbers or None (not strings or other types)
                assert isinstance(fundamental_summary[key], (int, float, type(None)))
    
    def test_caching_and_performance(self, fundamental_analyzer, sample_stock_data):
        """Test that fundamental analysis supports caching for performance"""
        # Test that repeated calls are cached to avoid redundant API calls
        
        with patch('yfinance.Ticker') as mock_ticker_class:
            mock_ticker = Mock()
            mock_ticker.info = {'forwardPE': 25.0}
            mock_ticker_class.return_value = mock_ticker
            
            stock_data = StockData("TEST.BO", sample_stock_data, "1y")
            
            # First call
            summary1 = fundamental_analyzer.get_fundamental_summary(stock_data)
            
            # Second call should use cache (implementation dependent)
            summary2 = fundamental_analyzer.get_fundamental_summary(stock_data)
            
            # Should return same results
            assert summary1 == summary2
            
            # Should have made only one API call (if caching is implemented)
            # This test defines the expected behavior