import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

@pytest.fixture
def sample_stock_data():
    """Sample stock data for testing"""
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    rng = np.random.default_rng(42)
    
    # Generate realistic stock price data
    base_price = 100
    returns = rng.normal(0.001, 0.02, len(dates))
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # Create volume data
    volumes = rng.integers(100000, 1000000, size=len(dates))
    
    data = pd.DataFrame({
        'Date': dates,
        'Open': [p * rng.uniform(0.98, 1.02) for p in prices],
        'High': [p * rng.uniform(1.01, 1.05) for p in prices],
        'Low': [p * rng.uniform(0.95, 0.99) for p in prices],
        'Close': prices,
        'Volume': volumes,
        'Adj Close': prices
    })
    
    data.set_index('Date', inplace=True)
    return data

@pytest.fixture
def bse_symbols():
    """Common BSE stock symbols for testing"""
    return [
        "RELIANCE.BO",
        "TCS.BO",
        "INFY.BO",
        "HDFC.BO",
        "WIPRO.BO"
    ]

@pytest.fixture
def sample_analysis_result():
    """Sample analysis result for CLI testing"""
    return {
        'symbol': 'RELIANCE.BO',
        'current_price': 2654.50,
        'sma_20': 2620.0,
        'sma_50': 2580.0,
        'sma_200': 2450.0,
        'rsi': 42.5,
        'macd_line': 15.25,
        'macd_signal': 12.80,
        'macd_histogram': 2.45,
        'macd_crossover': 1,
        'bb_upper': 2720.5,
        'bb_middle': 2620.0,
        'bb_lower': 2519.5,
        'volume_sma': 850000,
        'relative_volume': 1.4,
        'trend_signal': 'bullish',
        'momentum_signal': 'bullish',
        'recommendation': 'BUY',
        'confidence': 0.78,
        'target_price': 2850.0,
        'stop_loss': 2500.0,
        'risk_level': 'MEDIUM'
    }

@pytest.fixture
def sample_cli_args():
    """Sample CLI argument combinations for testing"""
    return {
        'basic': ['RELIANCE.BO'],
        'with_period': ['RELIANCE.BO', '--period', '2y'],
        'detailed': ['TCS.BO', '--detailed'],
        'json_format': ['INFY.BO', '--format', 'json'],
        'csv_export': ['HDFC.BO', '--export', 'csv'],
        'full_options': ['WIPRO.BO', '--period', '1y', '--risk', 'moderate', '--format', 'table', '--detailed']
    }

@pytest.fixture
def expected_terminal_output():
    """Expected terminal output format for testing"""
    return """BSE Stock Analysis: RELIANCE.BO
═══════════════════════════════════════════════════════════════

Company: Reliance Industries Ltd                    Last Updated: 15-Jan-2025 10:30 AM
Current Price: ₹2,654.50 (↗ +1.2%)                 Volume: 1.2M (Above Average)

┌─ RECOMMENDATION ────────────────────────────────────────────────────┐
│ 📈 BUY (Confidence: 78%)                                           │
│ Target Price: ₹2,850 - ₹3,200 (6-12 months)                       │
│ Risk Level: MEDIUM                                                  │
└─────────────────────────────────────────────────────────────────────┘

┌─ KEY METRICS ───────────────────────────────────────────────────────┐
│ Technical Indicators          │ Risk Assessment                     │
│ ────────────────────────────  │ ─────────────────────────────────  │
│ RSI (14):           42.5      │ Beta:               1.15           │
│ MACD Signal:        BULLISH   │ Volatility:         28.5%          │
│ 20-day SMA:         ₹2,620    │ Max Drawdown:       -18.2%         │
│ 50-day SMA:         ₹2,580    │ Sharpe Ratio:       0.45           │
│ 200-day SMA:        ₹2,450    │ Risk Rating:        MEDIUM          │
└─────────────────────────────────────────────────────────────────────┘"""

@pytest.fixture
def expected_json_output():
    """Expected JSON output format for testing"""
    return {
        "symbol": "RELIANCE.BO",
        "timestamp": "2025-01-15T10:30:00Z",
        "current_price": 2654.50,
        "recommendation": {
            "action": "BUY",
            "confidence": 0.78,
            "target_price": 2850.0,
            "stop_loss": 2500.0,
            "risk_level": "MEDIUM"
        },
        "technical_indicators": {
            "sma_20": 2620.0,
            "sma_50": 2580.0,
            "sma_200": 2450.0,
            "rsi": 42.5,
            "macd": {
                "line": 15.25,
                "signal": 12.80,
                "histogram": 2.45,
                "crossover": 1
            },
            "bollinger_bands": {
                "upper": 2720.5,
                "middle": 2620.0,
                "lower": 2519.5
            }
        },
        "volume_analysis": {
            "current_volume": 1200000,
            "volume_sma": 850000,
            "relative_volume": 1.4
        },
        "signals": {
            "trend_signal": "bullish",
            "momentum_signal": "bullish"
        }
    }