from setuptools import setup, find_packages

setup(
    name="stock-analyzer",
    version="0.1.0",
    description="BSE Stock Analysis Tool",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    install_requires=[
        "yfinance>=0.2.18",
        "pandas>=2.0.0",
        "numpy>=1.24.0",
        "click>=8.0.0",
    ],
    entry_points={
        "console_scripts": [
            "stock-analyzer=src.cli.commands:cli",
        ],
    },
    python_requires=">=3.8",
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
)