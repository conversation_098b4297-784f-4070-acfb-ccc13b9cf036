# BSE Stock Analyzer

A command-line tool that analyzes BSE (Bombay Stock Exchange) stocks and provides buy/sell/hold recommendations using technical indicators.

## Quick Setup

```bash
# Simple way - no installation needed
python main.py analyze RELIANCE.BO

# Or install the package (optional)
pip install -e .
stock-analyzer analyze RELIANCE.BO
```

## Basic Usage

```bash
# Basic analysis
python main.py analyze RELIANCE.BO

# JSON output
python main.py analyze TCS.BO --format json

# Detailed analysis
python main.py analyze INFY.BO --enhanced

# Save to file
python main.py analyze WIPRO.BO --output report.csv

# Different time periods and risk levels
python main.py analyze WIPRO.BO --period 2y --risk aggressive
```

## Available Options

- `--period`: Analysis period (`1m`, `3m`, `6m`, `1y`, `2y`) - Default: `1y`
- `--risk`: Risk tolerance (`conservative`, `moderate`, `aggressive`) - Default: `moderate`
- `--format`: Output format (`table`, `json`, `csv`) - Default: `table`
- `--detailed`: Show detailed technical analysis
- `--output`: Save results to file

## Valid BSE Symbols

Use BSE stock symbols with `.BO` suffix:
- `RELIANCE.BO` (Reliance Industries)
- `TCS.BO` (Tata Consultancy Services)
- `INFY.BO` (Infosys)
- `HDFC.BO` (HDFC Bank)
- `WIPRO.BO` (Wipro)

## Help

```bash
python main.py --help
python main.py analyze --help
```